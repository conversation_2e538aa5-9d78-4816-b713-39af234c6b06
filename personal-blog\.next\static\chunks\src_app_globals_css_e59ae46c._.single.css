/* [project]/src/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-border-style: solid;
      --tw-leading: initial;
      --tw-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-outline-style: solid;
      --tw-duration: initial;
    }

    ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-border-style: solid;
      --tw-leading: initial;
      --tw-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-outline-style: solid;
      --tw-duration: initial;
    }
  }
}

.sr-only {
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  position: absolute;
  overflow: hidden;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.sticky {
  position: -webkit-sticky;
  position: sticky;
}

.top-1\/2 {
  top: 50%;
}

.top-1\/4 {
  top: 25%;
}

.right-1\/4 {
  right: 25%;
}

.bottom-1\/4 {
  bottom: 25%;
}

.left-1\/2 {
  left: 50%;
}

.left-1\/4 {
  left: 25%;
}

.-z-10 {
  z-index: calc(10 * -1);
}

.z-50 {
  z-index: 50;
}

.container {
  width: 100%;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.block {
  display: block;
}

.flex {
  display: flex;
}

.grid {
  display: grid;
}

.hidden {
  display: none;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.h-\[600px\] {
  height: 600px;
}

.h-auto {
  height: auto;
}

.h-full {
  height: 100%;
}

.min-h-\[calc\(100vh-8rem\)\] {
  min-height: calc(100vh - 8rem);
}

.min-h-screen {
  min-height: 100vh;
}

.w-\[600px\] {
  width: 600px;
}

.w-full {
  width: 100%;
}

.flex-1 {
  flex: 1;
}

.-translate-x-1\/2 {
  --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
  translate: var(--tw-translate-x) var(--tw-translate-y);
}

.-translate-y-1\/2 {
  --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
  translate: var(--tw-translate-x) var(--tw-translate-y);
}

.scale-0 {
  --tw-scale-x: 0%;
  --tw-scale-y: 0%;
  --tw-scale-z: 0%;
  scale: var(--tw-scale-x) var(--tw-scale-y);
}

.scale-100 {
  --tw-scale-x: 100%;
  --tw-scale-y: 100%;
  --tw-scale-z: 100%;
  scale: var(--tw-scale-x) var(--tw-scale-y);
}

.rotate-0 {
  rotate: none;
}

.rotate-90 {
  rotate: 90deg;
}

.transform {
  transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.justify-start {
  justify-content: flex-start;
}

.overflow-hidden {
  overflow: hidden;
}

.rounded-full {
  border-radius: 3.40282e38px;
}

.border {
  border-style: var(--tw-border-style);
  border-width: 1px;
}

.border-2 {
  border-style: var(--tw-border-style);
  border-width: 2px;
}

.border-t {
  border-top-style: var(--tw-border-style);
  border-top-width: 1px;
}

.border-b {
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 1px;
}

.bg-gradient-to-br {
  --tw-gradient-position: to bottom right in oklab;
  background-image: linear-gradient(var(--tw-gradient-stops));
}

.bg-gradient-to-r {
  --tw-gradient-position: to right in oklab;
  background-image: linear-gradient(var(--tw-gradient-stops));
}

.bg-\[length\:200\%_200\%\] {
  background-size: 200% 200%;
}

.bg-clip-text {
  -webkit-background-clip: text;
  background-clip: text;
}

.text-center {
  text-align: center;
}

.leading-none {
  --tw-leading: 1;
  line-height: 1;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.text-transparent {
  color: rgba(0, 0, 0, 0);
}

.underline {
  -webkit-text-decoration-line: underline;
  text-decoration-line: underline;
}

.underline-offset-4 {
  text-underline-offset: 4px;
}

.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ring {
  --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

.outline {
  outline-style: var(--tw-outline-style);
  outline-width: 1px;
}

.transition-all {
  transition-property: all;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}

.transition-colors {
  transition-property: color, background-color, border-color, outline-color, -webkit-text-decoration-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}

.transition-transform {
  transition-property: transform, translate, scale, rotate;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}

.duration-200 {
  --tw-duration: .2s;
  transition-duration: .2s;
}

.duration-300 {
  --tw-duration: .3s;
  transition-duration: .3s;
}

.duration-500 {
  --tw-duration: .5s;
  transition-duration: .5s;
}

@media (hover: hover) {
  .group-hover\:scale-105:is(:where(.group):hover *) {
    --tw-scale-x: 105%;
    --tw-scale-y: 105%;
    --tw-scale-z: 105%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
}

@media (hover: hover) {
  .group-hover\:scale-110:is(:where(.group):hover *) {
    --tw-scale-x: 110%;
    --tw-scale-y: 110%;
    --tw-scale-z: 110%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
}

.peer-disabled\:cursor-not-allowed:is(:where(.peer):disabled ~ *) {
  cursor: not-allowed;
}

.peer-disabled\:opacity-70:is(:where(.peer):disabled ~ *) {
  opacity: .7;
}

.file\:border-0::-webkit-file-upload-button {
  border-style: var(--tw-border-style);
  border-width: 0;
}

.file\:border-0::file-selector-button {
  border-style: var(--tw-border-style);
  border-width: 0;
}

.file\:bg-transparent::-webkit-file-upload-button {
  background-color: rgba(0, 0, 0, 0);
}

.file\:bg-transparent::file-selector-button {
  background-color: rgba(0, 0, 0, 0);
}

@media (hover: hover) {
  .hover\:scale-105:hover {
    --tw-scale-x: 105%;
    --tw-scale-y: 105%;
    --tw-scale-z: 105%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
}

@media (hover: hover) {
  .hover\:bg-transparent:hover {
    background-color: rgba(0, 0, 0, 0);
  }
}

@media (hover: hover) {
  .hover\:underline:hover {
    -webkit-text-decoration-line: underline;
    text-decoration-line: underline;
  }
}

.focus-visible\:ring-2:focus-visible {
  --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

.focus-visible\:ring-offset-2:focus-visible {
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
}

.focus-visible\:outline-none:focus-visible {
  --tw-outline-style: none;
  outline-style: none;
}

.active\:scale-95:active {
  --tw-scale-x: 95%;
  --tw-scale-y: 95%;
  --tw-scale-z: 95%;
  scale: var(--tw-scale-x) var(--tw-scale-y);
}

.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.disabled\:opacity-50:disabled {
  opacity: .5;
}

@media (prefers-color-scheme: dark) {
  .dark\:scale-0 {
    --tw-scale-x: 0%;
    --tw-scale-y: 0%;
    --tw-scale-z: 0%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
}

@media (prefers-color-scheme: dark) {
  .dark\:scale-100 {
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
}

@media (prefers-color-scheme: dark) {
  .dark\:-rotate-90 {
    rotate: -90deg;
  }
}

@media (prefers-color-scheme: dark) {
  .dark\:rotate-0 {
    rotate: none;
  }
}

:root {
  --background: 255 255 255;
  --foreground: 15 23 42;
  --card: 255 255 255;
  --card-foreground: 15 23 42;
  --popover: 255 255 255;
  --popover-foreground: 15 23 42;
  --primary: 59 130 246;
  --primary-foreground: 255 255 255;
  --primary-hover: 37 99 235;
  --secondary: 248 250 252;
  --secondary-foreground: 51 65 85;
  --secondary-hover: 241 245 249;
  --muted: 248 250 252;
  --muted-foreground: 100 116 139;
  --accent: 99 102 241;
  --accent-foreground: 255 255 255;
  --accent-hover: 79 70 229;
  --destructive: 239 68 68;
  --destructive-foreground: 248 250 252;
  --success: 34 197 94;
  --success-foreground: 255 255 255;
  --warning: 245 158 11;
  --warning-foreground: 255 255 255;
  --border: 226 232 240;
  --border-hover: 203 213 225;
  --input: 241 245 249;
  --input-border: 203 213 225;
  --ring: 59 130 246;
  --radius: .75rem;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, .05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, .1), 0 1px 2px -1px rgba(0, 0, 0, .1);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, .1), 0 2px 4px -2px rgba(0, 0, 0, .1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, .1), 0 4px 6px -4px rgba(0, 0, 0, .1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, .1), 0 8px 10px -6px rgba(0, 0, 0, .1);
}

.dark {
  --background: 3 7 18;
  --foreground: 248 250 252;
  --card: 15 23 42;
  --card-foreground: 248 250 252;
  --popover: 15 23 42;
  --popover-foreground: 248 250 252;
  --primary: 99 102 241;
  --primary-foreground: 255 255 255;
  --primary-hover: 129 140 248;
  --secondary: 30 41 59;
  --secondary-foreground: 203 213 225;
  --secondary-hover: 51 65 85;
  --muted: 30 41 59;
  --muted-foreground: 148 163 184;
  --accent: 59 130 246;
  --accent-foreground: 255 255 255;
  --accent-hover: 96 165 250;
  --destructive: 248 113 113;
  --destructive-foreground: 15 23 42;
  --success: 74 222 128;
  --success-foreground: 15 23 42;
  --warning: 251 191 36;
  --warning-foreground: 15 23 42;
  --border: 51 65 85;
  --border-hover: 71 85 105;
  --input: 30 41 59;
  --input-border: 71 85 105;
  --ring: 99 102 241;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, .3);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, .4), 0 1px 2px -1px rgba(0, 0, 0, .4);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, .4), 0 2px 4px -2px rgba(0, 0, 0, .4);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, .4), 0 4px 6px -4px rgba(0, 0, 0, .4);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, .4), 0 8px 10px -6px rgba(0, 0, 0, .4);
}

* {
  border-color: hsl(var(--border));
}

html {
  scroll-behavior: smooth;
}

body {
  background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--muted)) 100%);
  color: hsl(var(--foreground));
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: "cv02", "cv03", "cv04", "cv11";
  font-variant-numeric: oldstyle-nums;
  background-attachment: fixed;
  font-family: Inter, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif;
  line-height: 1.7;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
  border-radius: 4px;
}

::selection {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

h1, h2, h3, h4, h5, h6 {
  letter-spacing: -.04em;
  background: linear-gradient(135deg, hsl(var(--foreground)) 0%, hsl(var(--primary)) 100%);
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  font-weight: 700;
  line-height: 1.1;
  animation: 8s ease-in-out infinite gradient-shift;
}

h1 {
  letter-spacing: -.06em;
  font-size: max(2.5rem, min(5vw, 4rem));
  font-weight: 800;
}

h2 {
  font-size: max(2rem, min(4vw, 3rem));
  font-weight: 700;
}

h3 {
  font-size: max(1.5rem, min(3vw, 2rem));
  font-weight: 600;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px)scale(.95);
  }

  to {
    opacity: 1;
    transform: translateY(0)scale(1);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(.9);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0%;
  }

  50% {
    background-position: 100%;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px hsl(var(--primary) / .3);
  }

  50% {
    box-shadow: 0 0 40px hsl(var(--primary) / .6);
  }
}

.animate-fade-in {
  animation: .6s cubic-bezier(.4, 0, .2, 1) fadeIn;
}

.animate-slide-in {
  animation: .4s cubic-bezier(.4, 0, .2, 1) slideIn;
}

.animate-slide-up {
  animation: .5s cubic-bezier(.4, 0, .2, 1) slideUp;
}

.animate-scale-in {
  animation: .3s cubic-bezier(.4, 0, .2, 1) scaleIn;
}

.animate-float {
  animation: 6s ease-in-out infinite float;
}

.animate-pulse-glow {
  animation: 2s ease-in-out infinite pulse-glow;
}

.prose {
  max-width: none;
  color: hsl(var(--foreground));
}

.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
  color: hsl(var(--foreground));
  margin-top: 2rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.prose p {
  margin-bottom: 1.5rem;
  line-height: 1.7;
}

.prose a {
  color: hsl(var(--primary));
  text-underline-offset: 2px;
  text-decoration: underline;
  transition: all .2s;
}

.prose a:hover {
  color: hsl(var(--primary));
  text-decoration: none;
}

.prose blockquote {
  border-left: 4px solid hsl(var(--border));
  color: hsl(var(--muted-foreground));
  margin: 2rem 0;
  padding-left: 1.5rem;
  font-style: italic;
}

.prose code {
  background-color: hsl(var(--muted));
  border-radius: .25rem;
  padding: .2rem .4rem;
  font-family: SF Mono, Monaco, Cascadia Code, Roboto Mono, Consolas, Courier New, monospace;
  font-size: .875em;
}

.prose pre {
  background-color: hsl(var(--muted));
  border-radius: .5rem;
  margin: 2rem 0;
  padding: 1.5rem;
  overflow-x: auto;
}

.prose pre code {
  background-color: rgba(0, 0, 0, 0);
  padding: 0;
}

.prose img {
  border-radius: .5rem;
  margin: 2rem 0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, .1);
}

.prose ul, .prose ol {
  margin: 1.5rem 0;
  padding-left: 2rem;
}

.prose li {
  margin: .5rem 0;
}

.glass {
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, .1);
  border: 1px solid rgba(255, 255, 255, .2);
}

.dark .glass {
  background: rgba(0, 0, 0, .2);
  border: 1px solid rgba(255, 255, 255, .1);
}

.interactive-card {
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
}

.interactive-card:hover {
  transform: translateY(-8px)scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, .25);
}

.animate-gradient-shift {
  background-size: 200% 200%;
  animation: 8s ease-in-out infinite gradient-shift;
}

.stagger-1 {
  animation-delay: .1s;
}

.stagger-2 {
  animation-delay: .2s;
}

.stagger-3 {
  animation-delay: .3s;
}

.stagger-4 {
  animation-delay: .4s;
}

.stagger-5 {
  animation-delay: .5s;
}

.focus-enhanced:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 4px;
  box-shadow: 0 0 0 4px hsl(var(--ring) / .2);
}

.page-transition {
  animation: .5s ease-out fadeIn;
}

.loading-shimmer {
  background: linear-gradient(90deg, hsl(var(--muted)) 0%, hsl(var(--muted-foreground) / .1) 50%, hsl(var(--muted)) 100%);
  background-size: 200% 100%;
  animation: 2s infinite shimmer;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--accent)));
  border: 2px solid hsl(var(--muted));
  border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, hsl(var(--primary-hover)), hsl(var(--accent-hover)));
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-rotate-x {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-y {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-z {
  syntax: "*";
  inherits: false
}

@property --tw-skew-x {
  syntax: "*";
  inherits: false
}

@property --tw-skew-y {
  syntax: "*";
  inherits: false
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

/*# sourceMappingURL=src_app_globals_css_e59ae46c._.single.css.map*/