{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/personal-blog/src/components/providers/theme-provider.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport { ThemeProvider as NextThemesProvider } from 'next-themes'\nimport { type ThemeProviderProps } from 'next-themes/dist/types'\n\nexport function ThemeProvider({ children, ...props }: ThemeProviderProps) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAMO,SAAS,cAAc,KAA0C;QAA1C,EAAE,QAAQ,EAAE,GAAG,OAA2B,GAA1C;IAC5B,qBAAO,6LAAC,mJAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC;KAFgB", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/personal-blog/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient, createServerClient } from '@supabase/ssr'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Check if Supabase is properly configured\nconst isSupabaseConfigured = supabaseUrl &&\n  supabaseAnonKey &&\n  supabaseUrl !== 'your_supabase_project_url' &&\n  supabaseAnonKey !== 'your_supabase_anon_key'\n\n// Client-side Supabase client\nexport const supabase = isSupabaseConfigured ? createClient(supabaseUrl, supabaseAnonKey) : null\n\n// Browser client for client components\nexport const createClientComponentClient = () => {\n  if (!isSupabaseConfigured) return null\n  return createBrowserClient(supabaseUrl, supabaseAnonKey)\n}\n\n// Server client for server components (to be used in server components only)\nexport const createServerComponentClient = async () => {\n  if (!isSupabaseConfigured) return null\n\n  const { cookies } = await import('next/headers')\n  const cookieStore = cookies()\n\n  return createServerClient(supabaseUrl, supabaseAnonKey, {\n    cookies: {\n      get(name: string) {\n        return cookieStore.get(name)?.value\n      },\n    },\n  })\n}\n\n// Server client for route handlers\nexport const createRouteHandlerClient = (request: Request) => {\n  const response = new Response()\n  \n  return createServerClient(supabaseUrl, supabaseAnonKey, {\n    cookies: {\n      get(name: string) {\n        return request.headers.get('cookie')?.split(';')\n          .find(c => c.trim().startsWith(`${name}=`))\n          ?.split('=')[1]\n      },\n      set(name: string, value: string, options: any) {\n        response.headers.append('Set-Cookie', `${name}=${value}; ${Object.entries(options).map(([k, v]) => `${k}=${v}`).join('; ')}`)\n      },\n      remove(name: string, options: any) {\n        response.headers.append('Set-Cookie', `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; ${Object.entries(options).map(([k, v]) => `${k}=${v}`).join('; ')}`)\n      },\n    },\n  })\n}\n\nexport type Database = {\n  public: {\n    Tables: {\n      profiles: {\n        Row: {\n          id: string\n          email: string\n          full_name: string | null\n          avatar_url: string | null\n          role: 'admin' | 'user'\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id: string\n          email: string\n          full_name?: string | null\n          avatar_url?: string | null\n          role?: 'admin' | 'user'\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          email?: string\n          full_name?: string | null\n          avatar_url?: string | null\n          role?: 'admin' | 'user'\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      categories: {\n        Row: {\n          id: string\n          name: string\n          slug: string\n          description: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          name: string\n          slug: string\n          description?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          name?: string\n          slug?: string\n          description?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      posts: {\n        Row: {\n          id: string\n          title: string\n          slug: string\n          content: string\n          excerpt: string | null\n          featured_image: string | null\n          status: 'draft' | 'published'\n          author_id: string\n          category_id: string | null\n          created_at: string\n          updated_at: string\n          published_at: string | null\n          meta_title: string | null\n          meta_description: string | null\n        }\n        Insert: {\n          id?: string\n          title: string\n          slug: string\n          content: string\n          excerpt?: string | null\n          featured_image?: string | null\n          status?: 'draft' | 'published'\n          author_id: string\n          category_id?: string | null\n          created_at?: string\n          updated_at?: string\n          published_at?: string | null\n          meta_title?: string | null\n          meta_description?: string | null\n        }\n        Update: {\n          id?: string\n          title?: string\n          slug?: string\n          content?: string\n          excerpt?: string | null\n          featured_image?: string | null\n          status?: 'draft' | 'published'\n          author_id?: string\n          category_id?: string | null\n          created_at?: string\n          updated_at?: string\n          published_at?: string | null\n          meta_title?: string | null\n          meta_description?: string | null\n        }\n      }\n      comments: {\n        Row: {\n          id: string\n          content: string\n          post_id: string\n          author_id: string\n          parent_id: string | null\n          status: 'pending' | 'approved' | 'rejected'\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          content: string\n          post_id: string\n          author_id: string\n          parent_id?: string | null\n          status?: 'pending' | 'approved' | 'rejected'\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          content?: string\n          post_id?: string\n          author_id?: string\n          parent_id?: string | null\n          status?: 'pending' | 'approved' | 'rejected'\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      site_settings: {\n        Row: {\n          id: string\n          key: string\n          value: string\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          key: string\n          value: string\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          key?: string\n          value?: string\n          created_at?: string\n          updated_at?: string\n        }\n      }\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAGoB;AAFpB;AAAA;;;AAEA,MAAM;AACN,MAAM;AAEN,2CAA2C;AAC3C,MAAM,uBAAuB,eAC3B,mBACA,gBAAgB,+BAChB,oBAAoB;AAGf,MAAM,WAAW,sCAAuB,0BAA6C;AAGrF,MAAM,8BAA8B;IACzC,wCAA2B,OAAO;;;AAEpC;AAGO,MAAM,8BAA8B;IACzC,wCAA2B,OAAO;;;IAElC,MAAQ;IACR,MAAM;AASR;AAGO,MAAM,2BAA2B,CAAC;IACvC,MAAM,WAAW,IAAI;IAErB,OAAO,CAAA,GAAA,4KAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,iBAAiB;QACtD,SAAS;YACP,KAAI,IAAY;oBACP,iCAAA;gBAAP,QAAO,uBAAA,QAAQ,OAAO,CAAC,GAAG,CAAC,uBAApB,4CAAA,kCAAA,qBAA+B,KAAK,CAAC,KACzC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,GAAG,UAAU,CAAC,AAAC,GAAO,OAAL,MAAK,oBADlC,sDAAA,gCAEH,KAAK,CAAC,IAAI,CAAC,EAAE;YACnB;YACA,KAAI,IAAY,EAAE,KAAa,EAAE,OAAY;gBAC3C,SAAS,OAAO,CAAC,MAAM,CAAC,cAAc,AAAC,GAAU,OAAR,MAAK,KAAa,OAAV,OAAM,MAAoE,OAAhE,OAAO,OAAO,CAAC,SAAS,GAAG,CAAC;wBAAC,CAAC,GAAG,EAAE;2BAAK,AAAC,GAAO,OAAL,GAAE,KAAK,OAAF;mBAAK,IAAI,CAAC;YACvH;YACA,QAAO,IAAY,EAAE,OAAY;gBAC/B,SAAS,OAAO,CAAC,MAAM,CAAC,cAAc,AAAC,GAAmD,OAAjD,MAAK,8CAA4G,OAAhE,OAAO,OAAO,CAAC,SAAS,GAAG,CAAC;wBAAC,CAAC,GAAG,EAAE;2BAAK,AAAC,GAAO,OAAL,GAAE,KAAK,OAAF;mBAAK,IAAI,CAAC;YACtJ;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/personal-blog/src/hooks/use-auth.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { User } from '@supabase/supabase-js'\nimport { createClientComponentClient } from '@/lib/supabase'\nimport { AuthUser } from '@/types'\n\ninterface AuthContextType {\n  user: AuthUser | null\n  loading: boolean\n  signIn: (email: string, password: string) => Promise<{ error: any }>\n  signUp: (email: string, password: string, fullName?: string) => Promise<{ error: any }>\n  signOut: () => Promise<void>\n  resetPassword: (email: string) => Promise<{ error: any }>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<AuthUser | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  // Check if Supabase is configured\n  const isSupabaseConfigured = process.env.NEXT_PUBLIC_SUPABASE_URL &&\n    process.env.NEXT_PUBLIC_SUPABASE_URL !== 'your_supabase_project_url'\n\n  const supabase = isSupabaseConfigured ? createClientComponentClient() : null\n\n  useEffect(() => {\n    if (!supabase) {\n      setLoading(false)\n      return\n    }\n\n    // Get initial session\n    const getInitialSession = async () => {\n      const { data: { session } } = await supabase.auth.getSession()\n      if (session?.user) {\n        await fetchUserProfile(session.user)\n      }\n      setLoading(false)\n    }\n\n    getInitialSession()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        if (session?.user) {\n          await fetchUserProfile(session.user)\n        } else {\n          setUser(null)\n        }\n        setLoading(false)\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [supabase])\n\n  const fetchUserProfile = async (authUser: User) => {\n    if (!supabase) return\n\n    try {\n      const { data: profile } = await supabase\n        .from('profiles')\n        .select('*')\n        .eq('id', authUser.id)\n        .single()\n\n      if (profile) {\n        setUser({\n          id: profile.id,\n          email: profile.email,\n          role: profile.role,\n          full_name: profile.full_name,\n          avatar_url: profile.avatar_url,\n        })\n      }\n    } catch (error) {\n      console.error('Error fetching user profile:', error)\n    }\n  }\n\n  const signIn = async (email: string, password: string) => {\n    if (!supabase) return { error: new Error('Supabase not configured') }\n\n    const { error } = await supabase.auth.signInWithPassword({\n      email,\n      password,\n    })\n    return { error }\n  }\n\n  const signUp = async (email: string, password: string, fullName?: string) => {\n    if (!supabase) return { error: new Error('Supabase not configured') }\n\n    const { error } = await supabase.auth.signUp({\n      email,\n      password,\n      options: {\n        data: {\n          full_name: fullName,\n        },\n      },\n    })\n    return { error }\n  }\n\n  const signOut = async () => {\n    if (!supabase) return\n\n    await supabase.auth.signOut()\n    setUser(null)\n  }\n\n  const resetPassword = async (email: string) => {\n    if (!supabase) return { error: new Error('Supabase not configured') }\n\n    const { error } = await supabase.auth.resetPasswordForEmail(email, {\n      redirectTo: `${window.location.origin}/auth/reset-password`,\n    })\n    return { error }\n  }\n\n  const value = {\n    user,\n    loading,\n    signIn,\n    signUp,\n    signOut,\n    resetPassword,\n  }\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;AAuB+B;;AArB/B;AAEA;;;AAJA;;;AAgBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;;IAC3B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,kCAAkC;IAClC,MAAM,uBAAuB,iEAC3B,kEAAyC;IAE3C,MAAM,WAAW,sCAAuB,0BAAgC;IAExE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,wCAAe;gBACb,WAAW;gBACX;YACF;;;YAEA,sBAAsB;YACtB,MAAM;YAUN,0BAA0B;YAC1B,MAAgB;QAYlB;iCAAG;QAAC;KAAS;IAEb,MAAM,mBAAmB,OAAO;QAC9B,wCAAe;;;IAqBjB;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,wCAAe,OAAO;YAAE,OAAO,IAAI,MAAM;QAA2B;;;QAEpE,MAAQ;IAKV;IAEA,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,wCAAe,OAAO;YAAE,OAAO,IAAI,MAAM;QAA2B;;;QAEpE,MAAQ;IAUV;IAEA,MAAM,UAAU;QACd,wCAAe;;;IAIjB;IAEA,MAAM,gBAAgB,OAAO;QAC3B,wCAAe,OAAO;YAAE,OAAO,IAAI,MAAM;QAA2B;;;QAEpE,MAAQ;IAIV;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;GArHgB;KAAA;AAuHT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/personal-blog/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: string | Date) {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(new Date(date))\n}\n\nexport function formatDateShort(date: string | Date) {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  }).format(new Date(date))\n}\n\nexport function generateSlug(title: string): string {\n  return title\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '') // Remove special characters\n    .replace(/\\s+/g, '-') // Replace spaces with hyphens\n    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen\n    .trim()\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text\n  return text.slice(0, maxLength).trim() + '...'\n}\n\nexport function extractExcerpt(content: string, maxLength: number = 160): string {\n  // Remove HTML tags and extract plain text\n  const plainText = content.replace(/<[^>]*>/g, '').replace(/\\s+/g, ' ').trim()\n  return truncateText(plainText, maxLength)\n}\n\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout | null = null\n  \n  return (...args: Parameters<T>) => {\n    if (timeout) clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(' ')\n    .map(word => word.charAt(0))\n    .join('')\n    .toUpperCase()\n    .slice(0, 2)\n}\n\nexport function getReadingTime(content: string): number {\n  const wordsPerMinute = 200\n  const words = content.trim().split(/\\s+/).length\n  return Math.ceil(words / wordsPerMinute)\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,gBAAgB,IAAmB;IACjD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,aAAa,KAAa;IACxC,OAAO,MACJ,WAAW,GACX,OAAO,CAAC,aAAa,IAAI,4BAA4B;KACrD,OAAO,CAAC,QAAQ,KAAK,8BAA8B;KACnD,OAAO,CAAC,OAAO,KAAK,8CAA8C;KAClE,IAAI;AACT;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,WAAW,IAAI,KAAK;AAC3C;AAEO,SAAS,eAAe,OAAe;QAAE,YAAA,iEAAoB;IAClE,0CAA0C;IAC1C,MAAM,YAAY,QAAQ,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,QAAQ,KAAK,IAAI;IAC3E,OAAO,aAAa,WAAW;AACjC;AAEO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI,UAAiC;IAErC,OAAO;yCAAI;YAAA;;QACT,IAAI,SAAS,aAAa;QAC1B,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;AACd;AAEO,SAAS,eAAe,OAAe;IAC5C,MAAM,iBAAiB;IACvB,MAAM,QAAQ,QAAQ,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM;IAChD,OAAO,KAAK,IAAI,CAAC,QAAQ;AAC3B", "debugId": null}}, {"offset": {"line": 292, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/personal-blog/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,6JAAA,CAAA,aAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 360, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/personal-blog/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useState } from 'react'\nimport { Menu, X, Sun, Moon, User, LogOut, Settings } from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport { Button } from '@/components/ui/button'\nimport { useAuth } from '@/hooks/use-auth'\nimport { cn } from '@/lib/utils'\n\nconst navigation = [\n  { name: 'Home', href: '/' },\n  { name: 'Blog', href: '/blog' },\n  { name: 'About', href: '/about' },\n  { name: 'Contact', href: '/contact' },\n]\n\nexport function Header() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)\n  const { theme, setTheme } = useTheme()\n  const { user, signOut, loading } = useAuth()\n  const pathname = usePathname()\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"container flex h-16 items-center justify-between\">\n        {/* Logo */}\n        <Link href=\"/\" className=\"flex items-center space-x-2\">\n          <div className=\"h-8 w-8 rounded-lg bg-primary flex items-center justify-center\">\n            <span className=\"text-primary-foreground font-bold text-lg\">B</span>\n          </div>\n          <span className=\"font-bold text-xl\">Blog</span>\n        </Link>\n\n        {/* Desktop Navigation */}\n        <nav className=\"hidden md:flex items-center space-x-8\">\n          {navigation.map((item) => (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={cn(\n                \"text-sm font-medium transition-colors hover:text-primary\",\n                pathname === item.href\n                  ? \"text-primary\"\n                  : \"text-muted-foreground\"\n              )}\n            >\n              {item.name}\n            </Link>\n          ))}\n        </nav>\n\n        {/* Desktop Actions */}\n        <div className=\"hidden md:flex items-center space-x-4\">\n          {/* Theme Toggle */}\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}\n            className=\"h-9 w-9\"\n          >\n            <Sun className=\"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n            <Moon className=\"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n            <span className=\"sr-only\">Toggle theme</span>\n          </Button>\n\n          {/* User Menu */}\n          <div className=\"flex items-center space-x-2\">\n            {loading ? (\n              <div className=\"h-8 w-16 bg-muted animate-pulse rounded\" />\n            ) : user ? (\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"text-sm text-muted-foreground\">\n                  Welcome, {user.full_name || user.email}\n                </span>\n                {user.role === 'admin' && (\n                  <Button variant=\"ghost\" size=\"sm\" asChild>\n                    <Link href=\"/admin\">Admin</Link>\n                  </Button>\n                )}\n                <Button variant=\"ghost\" size=\"sm\" onClick={() => signOut()}>\n                  <LogOut className=\"h-4 w-4 mr-2\" />\n                  Sign Out\n                </Button>\n              </div>\n            ) : (\n              <>\n                <Button variant=\"ghost\" size=\"sm\" asChild>\n                  <Link href=\"/auth/sign-in\">Sign In</Link>\n                </Button>\n                <Button size=\"sm\" asChild>\n                  <Link href=\"/auth/sign-up\">Sign Up</Link>\n                </Button>\n              </>\n            )}\n          </div>\n        </div>\n\n        {/* Mobile Menu Button */}\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          className=\"md:hidden\"\n          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n        >\n          {mobileMenuOpen ? (\n            <X className=\"h-5 w-5\" />\n          ) : (\n            <Menu className=\"h-5 w-5\" />\n          )}\n          <span className=\"sr-only\">Toggle menu</span>\n        </Button>\n      </div>\n\n      {/* Mobile Navigation */}\n      {mobileMenuOpen && (\n        <div className=\"md:hidden border-t bg-background\">\n          <div className=\"container py-4 space-y-4\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={cn(\n                  \"block text-sm font-medium transition-colors hover:text-primary\",\n                  pathname === item.href\n                    ? \"text-primary\"\n                    : \"text-muted-foreground\"\n                )}\n                onClick={() => setMobileMenuOpen(false)}\n              >\n                {item.name}\n              </Link>\n            ))}\n            <div className=\"pt-4 border-t space-y-2\">\n              {user ? (\n                <>\n                  <div className=\"text-sm text-muted-foreground px-2\">\n                    Welcome, {user.full_name || user.email}\n                  </div>\n                  {user.role === 'admin' && (\n                    <Button variant=\"ghost\" size=\"sm\" className=\"w-full justify-start\" asChild>\n                      <Link href=\"/admin\">Admin Panel</Link>\n                    </Button>\n                  )}\n                  <Button variant=\"ghost\" size=\"sm\" className=\"w-full justify-start\" onClick={() => signOut()}>\n                    <LogOut className=\"h-4 w-4 mr-2\" />\n                    Sign Out\n                  </Button>\n                </>\n              ) : (\n                <>\n                  <Button variant=\"ghost\" size=\"sm\" className=\"w-full justify-start\" asChild>\n                    <Link href=\"/auth/sign-in\">Sign In</Link>\n                  </Button>\n                  <Button size=\"sm\" className=\"w-full\" asChild>\n                    <Link href=\"/auth/sign-up\">Sign Up</Link>\n                  </Button>\n                </>\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAWA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAQ,MAAM;IAAQ;IAC9B;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEM,SAAS;;IACd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACzC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;;0CACvB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAA4C;;;;;;;;;;;0CAE9D,6LAAC;gCAAK,WAAU;0CAAoB;;;;;;;;;;;;kCAItC,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA,aAAa,KAAK,IAAI,GAClB,iBACA;0CAGL,KAAK,IAAI;+BATL,KAAK,IAAI;;;;;;;;;;kCAepB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,SAAS,UAAU,SAAS,UAAU;gCACrD,WAAU;;kDAEV,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAI5B,6LAAC;gCAAI,WAAU;0CACZ,wBACC,6LAAC;oCAAI,WAAU;;;;;2CACb,qBACF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;;gDAAgC;gDACpC,KAAK,SAAS,IAAI,KAAK,KAAK;;;;;;;wCAEvC,KAAK,IAAI,KAAK,yBACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,OAAO;sDACvC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAS;;;;;;;;;;;sDAGxB,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,SAAS,IAAM;;8DAC/C,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;yDAKvC;;sDACE,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,OAAO;sDACvC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAgB;;;;;;;;;;;sDAE7B,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,OAAO;sDACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;kCAQrC,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,kBAAkB,CAAC;;4BAEjC,+BACC,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAEb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAElB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;YAK7B,gCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA,aAAa,KAAK,IAAI,GAClB,iBACA;gCAEN,SAAS,IAAM,kBAAkB;0CAEhC,KAAK,IAAI;+BAVL,KAAK,IAAI;;;;;sCAalB,6LAAC;4BAAI,WAAU;sCACZ,qBACC;;kDACE,6LAAC;wCAAI,WAAU;;4CAAqC;4CACxC,KAAK,SAAS,IAAI,KAAK,KAAK;;;;;;;oCAEvC,KAAK,IAAI,KAAK,yBACb,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAK,WAAU;wCAAuB,OAAO;kDACxE,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDAAS;;;;;;;;;;;kDAGxB,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAK,WAAU;wCAAuB,SAAS,IAAM;;0DAChF,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;6DAKvC;;kDACE,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAK,WAAU;wCAAuB,OAAO;kDACxE,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDAAgB;;;;;;;;;;;kDAE7B,6LAAC,qIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,WAAU;wCAAS,OAAO;kDAC1C,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU/C;GApJgB;;QAEc,mJAAA,CAAA,WAAQ;QACD,+HAAA,CAAA,UAAO;QACzB,qIAAA,CAAA,cAAW;;;KAJd", "debugId": null}}]}