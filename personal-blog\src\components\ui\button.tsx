import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-semibold ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 active:scale-95",
  {
    variants: {
      variant: {
        default: "bg-gradient-to-r from-primary to-primary-hover text-primary-foreground shadow-md hover:shadow-lg hover:shadow-primary/25 hover:-translate-y-0.5",
        destructive:
          "bg-gradient-to-r from-destructive to-red-600 text-destructive-foreground shadow-md hover:shadow-lg hover:shadow-destructive/25 hover:-translate-y-0.5",
        outline:
          "border-2 border-input bg-background hover:bg-secondary hover:border-border-hover hover:shadow-md hover:-translate-y-0.5",
        secondary:
          "bg-gradient-to-r from-secondary to-secondary-hover text-secondary-foreground shadow-sm hover:shadow-md hover:-translate-y-0.5",
        ghost: "hover:bg-secondary hover:shadow-sm hover:-translate-y-0.5",
        link: "text-primary underline-offset-4 hover:underline hover:text-primary-hover",
        accent: "bg-gradient-to-r from-accent to-accent-hover text-accent-foreground shadow-md hover:shadow-lg hover:shadow-accent/25 hover:-translate-y-0.5",
        success: "bg-gradient-to-r from-success to-green-600 text-success-foreground shadow-md hover:shadow-lg hover:shadow-success/25 hover:-translate-y-0.5",
      },
      size: {
        default: "h-11 px-6 py-2.5",
        sm: "h-9 rounded-lg px-4 text-xs",
        lg: "h-13 rounded-xl px-10 text-base",
        icon: "h-11 w-11",
        xl: "h-16 rounded-2xl px-12 text-lg font-bold",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
