{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/personal-blog/src/components/providers/theme-provider.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport { ThemeProvider as NextThemesProvider } from 'next-themes'\nimport { type ThemeProviderProps } from 'next-themes/dist/types'\n\nexport function ThemeProvider({ children, ...props }: ThemeProviderProps) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAMO,SAAS,cAAc,KAA0C;QAA1C,EAAE,QAAQ,EAAE,GAAG,OAA2B,GAA1C;IAC5B,qBAAO,6LAAC,mJAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC;KAFgB", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/personal-blog/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: string | Date) {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(new Date(date))\n}\n\nexport function formatDateShort(date: string | Date) {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  }).format(new Date(date))\n}\n\nexport function generateSlug(title: string): string {\n  return title\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '') // Remove special characters\n    .replace(/\\s+/g, '-') // Replace spaces with hyphens\n    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen\n    .trim()\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text\n  return text.slice(0, maxLength).trim() + '...'\n}\n\nexport function extractExcerpt(content: string, maxLength: number = 160): string {\n  // Remove HTML tags and extract plain text\n  const plainText = content.replace(/<[^>]*>/g, '').replace(/\\s+/g, ' ').trim()\n  return truncateText(plainText, maxLength)\n}\n\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout | null = null\n  \n  return (...args: Parameters<T>) => {\n    if (timeout) clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(' ')\n    .map(word => word.charAt(0))\n    .join('')\n    .toUpperCase()\n    .slice(0, 2)\n}\n\nexport function getReadingTime(content: string): number {\n  const wordsPerMinute = 200\n  const words = content.trim().split(/\\s+/).length\n  return Math.ceil(words / wordsPerMinute)\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,gBAAgB,IAAmB;IACjD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,aAAa,KAAa;IACxC,OAAO,MACJ,WAAW,GACX,OAAO,CAAC,aAAa,IAAI,4BAA4B;KACrD,OAAO,CAAC,QAAQ,KAAK,8BAA8B;KACnD,OAAO,CAAC,OAAO,KAAK,8CAA8C;KAClE,IAAI;AACT;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,WAAW,IAAI,KAAK;AAC3C;AAEO,SAAS,eAAe,OAAe;QAAE,YAAA,iEAAoB;IAClE,0CAA0C;IAC1C,MAAM,YAAY,QAAQ,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,QAAQ,KAAK,IAAI;IAC3E,OAAO,aAAa,WAAW;AACjC;AAEO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI,UAAiC;IAErC,OAAO;yCAAI;YAAA;;QACT,IAAI,SAAS,aAAa;QAC1B,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;AACd;AAEO,SAAS,eAAe,OAAe;IAC5C,MAAM,iBAAiB;IACvB,MAAM,QAAQ,QAAQ,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM;IAChD,OAAO,KAAK,IAAI,CAAC,QAAQ;AAC3B", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/personal-blog/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,6JAAA,CAAA,aAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/personal-blog/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useState } from 'react'\nimport { Menu, X, Sun, Moon, User, LogOut, Settings } from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport { Button } from '@/components/ui/button'\nimport { useAuth } from '@/hooks/use-auth'\nimport { cn } from '@/lib/utils'\n\nconst navigation = [\n  { name: 'Home', href: '/' },\n  { name: 'Blog', href: '/blog' },\n  { name: 'About', href: '/about' },\n  { name: 'Contact', href: '/contact' },\n]\n\nexport function Header() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)\n  const { theme, setTheme } = useTheme()\n  const pathname = usePathname()\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"container flex h-16 items-center justify-between\">\n        {/* Logo */}\n        <Link href=\"/\" className=\"flex items-center space-x-2\">\n          <div className=\"h-8 w-8 rounded-lg bg-primary flex items-center justify-center\">\n            <span className=\"text-primary-foreground font-bold text-lg\">B</span>\n          </div>\n          <span className=\"font-bold text-xl\">Blog</span>\n        </Link>\n\n        {/* Desktop Navigation */}\n        <nav className=\"hidden md:flex items-center space-x-8\">\n          {navigation.map((item) => (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={cn(\n                \"text-sm font-medium transition-colors hover:text-primary\",\n                pathname === item.href\n                  ? \"text-primary\"\n                  : \"text-muted-foreground\"\n              )}\n            >\n              {item.name}\n            </Link>\n          ))}\n        </nav>\n\n        {/* Desktop Actions */}\n        <div className=\"hidden md:flex items-center space-x-4\">\n          {/* Theme Toggle */}\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}\n            className=\"h-9 w-9\"\n          >\n            <Sun className=\"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n            <Moon className=\"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n            <span className=\"sr-only\">Toggle theme</span>\n          </Button>\n\n          {/* User Menu - TODO: Replace with actual auth state */}\n          <div className=\"flex items-center space-x-2\">\n            <Button variant=\"ghost\" size=\"sm\">\n              Sign In\n            </Button>\n            <Button size=\"sm\">\n              Sign Up\n            </Button>\n          </div>\n        </div>\n\n        {/* Mobile Menu Button */}\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          className=\"md:hidden\"\n          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n        >\n          {mobileMenuOpen ? (\n            <X className=\"h-5 w-5\" />\n          ) : (\n            <Menu className=\"h-5 w-5\" />\n          )}\n          <span className=\"sr-only\">Toggle menu</span>\n        </Button>\n      </div>\n\n      {/* Mobile Navigation */}\n      {mobileMenuOpen && (\n        <div className=\"md:hidden border-t bg-background\">\n          <div className=\"container py-4 space-y-4\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={cn(\n                  \"block text-sm font-medium transition-colors hover:text-primary\",\n                  pathname === item.href\n                    ? \"text-primary\"\n                    : \"text-muted-foreground\"\n                )}\n                onClick={() => setMobileMenuOpen(false)}\n              >\n                {item.name}\n              </Link>\n            ))}\n            <div className=\"pt-4 border-t space-y-2\">\n              <Button variant=\"ghost\" size=\"sm\" className=\"w-full justify-start\">\n                Sign In\n              </Button>\n              <Button size=\"sm\" className=\"w-full\">\n                Sign Up\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;;;AATA;;;;;;;;AAWA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAQ,MAAM;IAAQ;IAC9B;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEM,SAAS;;IACd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;;0CACvB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAA4C;;;;;;;;;;;0CAE9D,6LAAC;gCAAK,WAAU;0CAAoB;;;;;;;;;;;;kCAItC,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA,aAAa,KAAK,IAAI,GAClB,iBACA;0CAGL,KAAK,IAAI;+BATL,KAAK,IAAI;;;;;;;;;;kCAepB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,SAAS,UAAU,SAAS,UAAU;gCACrD,WAAU;;kDAEV,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAI5B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;kDAAK;;;;;;kDAGlC,6LAAC,qIAAA,CAAA,SAAM;wCAAC,MAAK;kDAAK;;;;;;;;;;;;;;;;;;kCAOtB,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,kBAAkB,CAAC;;4BAEjC,+BACC,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAEb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAElB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;YAK7B,gCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA,aAAa,KAAK,IAAI,GAClB,iBACA;gCAEN,SAAS,IAAM,kBAAkB;0CAEhC,KAAK,IAAI;+BAVL,KAAK,IAAI;;;;;sCAalB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;8CAAuB;;;;;;8CAGnE,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,WAAU;8CAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnD;GA3GgB;;QAEc,mJAAA,CAAA,WAAQ;QACnB,qIAAA,CAAA,cAAW;;;KAHd", "debugId": null}}]}