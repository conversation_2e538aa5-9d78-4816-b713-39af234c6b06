{"version": 3, "sources": [], "sections": [{"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse, type NextRequest } from 'next/server'\n\nexport async function middleware(request: NextRequest) {\n  // For now, just pass through all requests\n  // TODO: Implement proper authentication middleware once Supabase is configured\n\n  // Skip middleware if Supabase is not configured\n  if (!process.env.NEXT_PUBLIC_SUPABASE_URL ||\n      process.env.NEXT_PUBLIC_SUPABASE_URL === 'your_supabase_project_url') {\n    return NextResponse.next()\n  }\n\n  // TODO: Add Supabase authentication logic here\n  return NextResponse.next()\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * Feel free to modify this pattern to include more paths.\n     */\n    '/((?!_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAEO,eAAe,WAAW,OAAoB;IACnD,0CAA0C;IAC1C,+EAA+E;IAE/E,gDAAgD;IAChD,wCAC0E;QACxE,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;;;AAIF;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}