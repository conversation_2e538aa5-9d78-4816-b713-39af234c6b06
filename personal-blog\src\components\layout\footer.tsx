import Link from 'next/link'
import { Gith<PERSON>, Twitter, Linkedin, Mail } from 'lucide-react'

const footerLinks = {
  blog: [
    { name: 'All Posts', href: '/blog' },
    { name: 'Categories', href: '/categories' },
    { name: 'Tags', href: '/tags' },
    { name: 'Archive', href: '/archive' },
  ],
  company: [
    { name: 'About', href: '/about' },
    { name: 'Contact', href: '/contact' },
    { name: 'Privacy Policy', href: '/privacy' },
    { name: 'Terms of Service', href: '/terms' },
  ],
  social: [
    { name: 'Twitter', href: '#', icon: Twitter },
    { name: 'GitHub', href: '#', icon: Github },
    { name: 'LinkedIn', href: '#', icon: Linkedin },
    { name: 'Email', href: 'mailto:<EMAIL>', icon: Mail },
  ],
}

export function Footer() {
  return (
    <footer className="border-t border-border/50 bg-gradient-to-br from-background to-muted/30 backdrop-blur-sm">
      <div className="container py-16">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="space-y-6">
            <Link href="/" className="flex items-center space-x-3 group">
              <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-primary to-accent flex items-center justify-center shadow-lg group-hover:shadow-xl group-hover:shadow-primary/25 transition-all duration-300 group-hover:scale-105">
                <span className="text-primary-foreground font-bold text-xl">B</span>
              </div>
              <span className="font-bold text-2xl bg-gradient-to-r from-foreground to-primary bg-clip-text text-transparent">Blog</span>
            </Link>
            <p className="text-base text-muted-foreground max-w-sm leading-relaxed">
              A modern, beautiful personal blog built with Next.js and Supabase.
              Sharing thoughts, ideas, and experiences with the world.
            </p>
          </div>

          {/* Blog Links */}
          <div className="space-y-4">
            <h3 className="font-semibold">Blog</h3>
            <ul className="space-y-2">
              {footerLinks.blog.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-sm text-muted-foreground hover:text-primary transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Company Links */}
          <div className="space-y-4">
            <h3 className="font-semibold">Company</h3>
            <ul className="space-y-2">
              {footerLinks.company.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-sm text-muted-foreground hover:text-primary transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Social Links */}
          <div className="space-y-6">
            <h3 className="font-bold text-lg">Connect</h3>
            <div className="flex space-x-3">
              {footerLinks.social.map((link) => {
                const Icon = link.icon
                return (
                  <Link
                    key={link.name}
                    href={link.href}
                    className="h-12 w-12 rounded-xl bg-secondary/50 hover:bg-primary hover:shadow-lg hover:shadow-primary/25 flex items-center justify-center text-muted-foreground hover:text-primary-foreground transition-all duration-300 hover:-translate-y-1 hover:scale-105"
                    target={link.href.startsWith('http') ? '_blank' : undefined}
                    rel={link.href.startsWith('http') ? 'noopener noreferrer' : undefined}
                  >
                    <Icon className="h-5 w-5" />
                    <span className="sr-only">{link.name}</span>
                  </Link>
                )
              })}
            </div>
          </div>
        </div>

        <div className="mt-8 pt-8 border-t">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-sm text-muted-foreground">
              © {new Date().getFullYear()} Personal Blog. All rights reserved.
            </p>
            <p className="text-sm text-muted-foreground">
              Built with ❤️ using Next.js and Supabase
            </p>
          </div>
        </div>
      </div>
    </footer>
  )
}
