{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/personal-blog/src/components/auth/sign-up-form.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const SignUpForm = registerClientReference(\n    function() { throw new Error(\"Attempted to call SignUpForm() from the server but SignUpForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/auth/sign-up-form.tsx <module evaluation>\",\n    \"SignUpForm\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,sEACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/personal-blog/src/components/auth/sign-up-form.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const SignUpForm = registerClientReference(\n    function() { throw new Error(\"Attempted to call SignUpForm() from the server but SignUpForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/auth/sign-up-form.tsx\",\n    \"SignUpForm\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,kDACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/personal-blog/src/app/auth/sign-up/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\nimport { SignUpForm } from '@/components/auth/sign-up-form'\n\nexport const metadata: Metadata = {\n  title: 'Sign Up',\n  description: 'Create a new account',\n}\n\nexport default function SignUpPage() {\n  return (\n    <div className=\"container flex items-center justify-center min-h-[calc(100vh-8rem)] py-8\">\n      <SignUpForm />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,gJAAA,CAAA,aAAU;;;;;;;;;;AAGjB", "debugId": null}}]}