{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/personal-blog/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-12 w-full rounded-xl border-2 border-input-border bg-input/50 backdrop-blur-sm px-4 py-3 text-sm font-medium ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:border-primary focus-visible:bg-background focus-visible:shadow-lg focus-visible:shadow-primary/10 disabled:cursor-not-allowed disabled:opacity-50 hover:border-border-hover hover:bg-background/80\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,6JAAA,CAAA,aAAgB,MAC5B,QAAgC;QAA/B,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO;IAC5B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kkBACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/personal-blog/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,6JAAA,CAAA,aAAgB,MAI5B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,oKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;;AAGb,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/personal-blog/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-2xl border border-border/50 bg-card/80 backdrop-blur-sm text-card-foreground shadow-lg hover:shadow-xl hover:shadow-primary/5 hover:-translate-y-1 transition-all duration-300 hover:border-border-hover\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-2 p-8\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-8 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-8 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,6JAAA,CAAA,aAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oNACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,aAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAAa,GAAG,KAAK;;;;;;;;AAEnF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,aAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,aAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,aAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/personal-blog/src/components/auth/sign-up-form.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { z } from 'zod'\nimport { toast } from 'react-hot-toast'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { useAuth } from '@/hooks/use-auth'\nimport { Eye, EyeOff, Loader2 } from 'lucide-react'\n\nconst signUpSchema = z.object({\n  fullName: z.string().min(2, 'Full name must be at least 2 characters'),\n  email: z.string().email('Please enter a valid email address'),\n  password: z.string().min(6, 'Password must be at least 6 characters'),\n  confirmPassword: z.string(),\n}).refine((data) => data.password === data.confirmPassword, {\n  message: \"Passwords don't match\",\n  path: [\"confirmPassword\"],\n})\n\ntype SignUpFormData = z.infer<typeof signUpSchema>\n\nexport function SignUpForm() {\n  const [showPassword, setShowPassword] = useState(false)\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false)\n  const [isLoading, setIsLoading] = useState(false)\n  const { signUp } = useAuth()\n  const router = useRouter()\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n  } = useForm<SignUpFormData>({\n    resolver: zodResolver(signUpSchema),\n  })\n\n  const onSubmit = async (data: SignUpFormData) => {\n    setIsLoading(true)\n    try {\n      const { error } = await signUp(data.email, data.password, data.fullName)\n      \n      if (error) {\n        toast.error(error.message || 'Failed to create account')\n      } else {\n        toast.success('Account created! Please check your email to verify your account.')\n        router.push('/auth/sign-in')\n      }\n    } catch (error) {\n      toast.error('An unexpected error occurred')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  return (\n    <Card className=\"w-full max-w-md mx-auto\">\n      <CardHeader className=\"text-center\">\n        <CardTitle className=\"text-2xl\">Create an account</CardTitle>\n        <CardDescription>\n          Join our community and start sharing your thoughts\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4\">\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"fullName\">Full Name</Label>\n            <Input\n              id=\"fullName\"\n              type=\"text\"\n              placeholder=\"Enter your full name\"\n              {...register('fullName')}\n              disabled={isLoading}\n            />\n            {errors.fullName && (\n              <p className=\"text-sm text-destructive\">{errors.fullName.message}</p>\n            )}\n          </div>\n\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"email\">Email</Label>\n            <Input\n              id=\"email\"\n              type=\"email\"\n              placeholder=\"Enter your email\"\n              {...register('email')}\n              disabled={isLoading}\n            />\n            {errors.email && (\n              <p className=\"text-sm text-destructive\">{errors.email.message}</p>\n            )}\n          </div>\n\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"password\">Password</Label>\n            <div className=\"relative\">\n              <Input\n                id=\"password\"\n                type={showPassword ? 'text' : 'password'}\n                placeholder=\"Create a password\"\n                {...register('password')}\n                disabled={isLoading}\n              />\n              <Button\n                type=\"button\"\n                variant=\"ghost\"\n                size=\"icon\"\n                className=\"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\"\n                onClick={() => setShowPassword(!showPassword)}\n                disabled={isLoading}\n              >\n                {showPassword ? (\n                  <EyeOff className=\"h-4 w-4\" />\n                ) : (\n                  <Eye className=\"h-4 w-4\" />\n                )}\n                <span className=\"sr-only\">\n                  {showPassword ? 'Hide password' : 'Show password'}\n                </span>\n              </Button>\n            </div>\n            {errors.password && (\n              <p className=\"text-sm text-destructive\">{errors.password.message}</p>\n            )}\n          </div>\n\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"confirmPassword\">Confirm Password</Label>\n            <div className=\"relative\">\n              <Input\n                id=\"confirmPassword\"\n                type={showConfirmPassword ? 'text' : 'password'}\n                placeholder=\"Confirm your password\"\n                {...register('confirmPassword')}\n                disabled={isLoading}\n              />\n              <Button\n                type=\"button\"\n                variant=\"ghost\"\n                size=\"icon\"\n                className=\"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\"\n                onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                disabled={isLoading}\n              >\n                {showConfirmPassword ? (\n                  <EyeOff className=\"h-4 w-4\" />\n                ) : (\n                  <Eye className=\"h-4 w-4\" />\n                )}\n                <span className=\"sr-only\">\n                  {showConfirmPassword ? 'Hide password' : 'Show password'}\n                </span>\n              </Button>\n            </div>\n            {errors.confirmPassword && (\n              <p className=\"text-sm text-destructive\">{errors.confirmPassword.message}</p>\n            )}\n          </div>\n\n          <Button type=\"submit\" className=\"w-full\" disabled={isLoading}>\n            {isLoading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n            Create Account\n          </Button>\n        </form>\n\n        <div className=\"mt-6 text-center text-sm\">\n          <p className=\"text-muted-foreground\">\n            Already have an account?{' '}\n            <Button variant=\"link\" className=\"p-0 h-auto font-normal\">\n              Sign in\n            </Button>\n          </p>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;AAbA;;;;;;;;;;;;;AAeA,MAAM,eAAe,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5B,UAAU,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,iBAAiB,gLAAA,CAAA,IAAC,CAAC,MAAM;AAC3B,GAAG,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,KAAK,eAAe,EAAE;IAC1D,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAIO,SAAS;;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACzB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAkB;QAC1B,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,KAAK,KAAK,EAAE,KAAK,QAAQ,EAAE,KAAK,QAAQ;YAEvE,IAAI,OAAO;gBACT,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC/B,OAAO;gBACL,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;kCAAW;;;;;;kCAChC,6LAAC,mIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,6LAAC,mIAAA,CAAA,cAAW;;kCACV,6LAAC;wBAAK,UAAU,aAAa;wBAAW,WAAU;;0CAChD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAW;;;;;;kDAC1B,6LAAC,oIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,aAAY;wCACX,GAAG,SAAS,WAAW;wCACxB,UAAU;;;;;;oCAEX,OAAO,QAAQ,kBACd,6LAAC;wCAAE,WAAU;kDAA4B,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;0CAIpE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAQ;;;;;;kDACvB,6LAAC,oIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,aAAY;wCACX,GAAG,SAAS,QAAQ;wCACrB,UAAU;;;;;;oCAEX,OAAO,KAAK,kBACX,6LAAC;wCAAE,WAAU;kDAA4B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;0CAIjE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAW;;;;;;kDAC1B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAM,eAAe,SAAS;gDAC9B,aAAY;gDACX,GAAG,SAAS,WAAW;gDACxB,UAAU;;;;;;0DAEZ,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,gBAAgB,CAAC;gDAChC,UAAU;;oDAET,6BACC,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;6EAElB,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEAEjB,6LAAC;wDAAK,WAAU;kEACb,eAAe,kBAAkB;;;;;;;;;;;;;;;;;;oCAIvC,OAAO,QAAQ,kBACd,6LAAC;wCAAE,WAAU;kDAA4B,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;0CAIpE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAkB;;;;;;kDACjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAM,sBAAsB,SAAS;gDACrC,aAAY;gDACX,GAAG,SAAS,kBAAkB;gDAC/B,UAAU;;;;;;0DAEZ,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,uBAAuB,CAAC;gDACvC,UAAU;;oDAET,oCACC,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;6EAElB,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEAEjB,6LAAC;wDAAK,WAAU;kEACb,sBAAsB,kBAAkB;;;;;;;;;;;;;;;;;;oCAI9C,OAAO,eAAe,kBACrB,6LAAC;wCAAE,WAAU;kDAA4B,OAAO,eAAe,CAAC,OAAO;;;;;;;;;;;;0CAI3E,6LAAC,qIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAS,WAAU;gCAAS,UAAU;;oCAChD,2BAAa,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA+B;;;;;;;;;;;;;kCAKpE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;gCAAwB;gCACV;8CACzB,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAO,WAAU;8CAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtE;GA1JgB;;QAIK,+HAAA,CAAA,UAAO;QACX,qIAAA,CAAA,YAAS;QAMpB,iKAAA,CAAA,UAAO;;;KAXG", "debugId": null}}]}