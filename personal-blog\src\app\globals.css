@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Light theme colors */
  --background: 255 255 255;
  --foreground: 15 23 42;
  --card: 255 255 255;
  --card-foreground: 15 23 42;
  --popover: 255 255 255;
  --popover-foreground: 15 23 42;
  --primary: 15 23 42;
  --primary-foreground: 248 250 252;
  --secondary: 241 245 249;
  --secondary-foreground: 15 23 42;
  --muted: 241 245 249;
  --muted-foreground: 100 116 139;
  --accent: 241 245 249;
  --accent-foreground: 15 23 42;
  --destructive: 239 68 68;
  --destructive-foreground: 248 250 252;
  --border: 226 232 240;
  --input: 226 232 240;
  --ring: 15 23 42;
  --radius: 0.5rem;
}

.dark {
  /* Dark theme colors */
  --background: 2 6 23;
  --foreground: 248 250 252;
  --card: 2 6 23;
  --card-foreground: 248 250 252;
  --popover: 2 6 23;
  --popover-foreground: 248 250 252;
  --primary: 248 250 252;
  --primary-foreground: 15 23 42;
  --secondary: 30 41 59;
  --secondary-foreground: 248 250 252;
  --muted: 30 41 59;
  --muted-foreground: 148 163 184;
  --accent: 30 41 59;
  --accent-foreground: 248 250 252;
  --destructive: 239 68 68;
  --destructive-foreground: 248 250 252;
  --border: 30 41 59;
  --input: 30 41 59;
  --ring: 212 212 216;
}

* {
  border-color: hsl(var(--border));
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--foreground));
}

/* Selection */
::selection {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

/* Focus styles */
:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  letter-spacing: -0.025em;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

/* Prose styles for blog content */
.prose {
  max-width: none;
  color: hsl(var(--foreground));
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  color: hsl(var(--foreground));
  font-weight: 600;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.prose p {
  margin-bottom: 1.5rem;
  line-height: 1.7;
}

.prose a {
  color: hsl(var(--primary));
  text-decoration: underline;
  text-underline-offset: 2px;
  transition: all 0.2s ease;
}

.prose a:hover {
  color: hsl(var(--primary));
  text-decoration: none;
}

.prose blockquote {
  border-left: 4px solid hsl(var(--border));
  padding-left: 1.5rem;
  margin: 2rem 0;
  font-style: italic;
  color: hsl(var(--muted-foreground));
}

.prose code {
  background-color: hsl(var(--muted));
  padding: 0.2rem 0.4rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.prose pre {
  background-color: hsl(var(--muted));
  padding: 1.5rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 2rem 0;
}

.prose pre code {
  background-color: transparent;
  padding: 0;
}

.prose img {
  border-radius: 0.5rem;
  margin: 2rem 0;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
}

.prose ul,
.prose ol {
  margin: 1.5rem 0;
  padding-left: 2rem;
}

.prose li {
  margin: 0.5rem 0;
}

/* Glass effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}
