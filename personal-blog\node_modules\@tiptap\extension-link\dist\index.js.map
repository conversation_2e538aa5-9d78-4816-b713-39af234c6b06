{"version": 3, "sources": ["../src/link.ts", "../src/helpers/autolink.ts", "../src/helpers/whitespace.ts", "../src/helpers/clickHandler.ts", "../src/helpers/pasteHandler.ts", "../src/index.ts"], "sourcesContent": ["import type { PasteRuleMatch } from '@tiptap/core'\nimport { <PERSON>, markPasteRule, mergeAttributes } from '@tiptap/core'\nimport type { Plugin } from '@tiptap/pm/state'\nimport { find, registerCustomProtocol, reset } from 'linkifyjs'\n\nimport { autolink } from './helpers/autolink.js'\nimport { clickHandler } from './helpers/clickHandler.js'\nimport { pasteHandler } from './helpers/pasteHandler.js'\nimport { UNICODE_WHITESPACE_REGEX_GLOBAL } from './helpers/whitespace.js'\n\nexport interface LinkProtocolOptions {\n  /**\n   * The protocol scheme to be registered.\n   * @default '''\n   * @example 'ftp'\n   * @example 'git'\n   */\n  scheme: string\n\n  /**\n   * If enabled, it allows optional slashes after the protocol.\n   * @default false\n   * @example true\n   */\n  optionalSlashes?: boolean\n}\n\nexport const pasteRegex =\n  /https?:\\/\\/(?:www\\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\\.[a-zA-Z]{2,}\\b(?:[-a-zA-Z0-9@:%._+~#=?!&/]*)(?:[-a-zA-Z0-9@:%._+~#=?!&/]*)/gi\n\n/**\n * @deprecated The default behavior is now to open links when the editor is not editable.\n */\ntype DeprecatedOpenWhenNotEditable = 'whenNotEditable'\n\nexport interface LinkOptions {\n  /**\n   * If enabled, the extension will automatically add links as you type.\n   * @default true\n   * @example false\n   */\n  autolink: boolean\n\n  /**\n   * An array of custom protocols to be registered with linkifyjs.\n   * @default []\n   * @example ['ftp', 'git']\n   */\n  protocols: Array<LinkProtocolOptions | string>\n\n  /**\n   * Default protocol to use when no protocol is specified.\n   * @default 'http'\n   */\n  defaultProtocol: string\n  /**\n   * If enabled, links will be opened on click.\n   * @default true\n   * @example false\n   */\n  openOnClick: boolean | DeprecatedOpenWhenNotEditable\n  /**\n   * If enabled, the link will be selected when clicked.\n   * @default false\n   * @example true\n   */\n  enableClickSelection: boolean\n  /**\n   * Adds a link to the current selection if the pasted content only contains an url.\n   * @default true\n   * @example false\n   */\n  linkOnPaste: boolean\n\n  /**\n   * HTML attributes to add to the link element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n\n  /**\n   * @deprecated Use the `shouldAutoLink` option instead.\n   * A validation function that modifies link verification for the auto linker.\n   * @param url - The url to be validated.\n   * @returns - True if the url is valid, false otherwise.\n   */\n  validate: (url: string) => boolean\n\n  /**\n   * A validation function which is used for configuring link verification for preventing XSS attacks.\n   * Only modify this if you know what you're doing.\n   *\n   * @returns {boolean} `true` if the URL is valid, `false` otherwise.\n   *\n   * @example\n   * isAllowedUri: (url, { defaultValidate, protocols, defaultProtocol }) => {\n   * return url.startsWith('./') || defaultValidate(url)\n   * }\n   */\n  isAllowedUri: (\n    /**\n     * The URL to be validated.\n     */\n    url: string,\n    ctx: {\n      /**\n       * The default validation function.\n       */\n      defaultValidate: (url: string) => boolean\n      /**\n       * An array of allowed protocols for the URL (e.g., \"http\", \"https\"). As defined in the `protocols` option.\n       */\n      protocols: Array<LinkProtocolOptions | string>\n      /**\n       * A string that represents the default protocol (e.g., 'http'). As defined in the `defaultProtocol` option.\n       */\n      defaultProtocol: string\n    },\n  ) => boolean\n\n  /**\n   * Determines whether a valid link should be automatically linked in the content.\n   *\n   * @param {string} url - The URL that has already been validated.\n   * @returns {boolean} - True if the link should be auto-linked; false if it should not be auto-linked.\n   */\n  shouldAutoLink: (url: string) => boolean\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    link: {\n      /**\n       * Set a link mark\n       * @param attributes The link attributes\n       * @example editor.commands.setLink({ href: 'https://tiptap.dev' })\n       */\n      setLink: (attributes: {\n        href: string\n        target?: string | null\n        rel?: string | null\n        class?: string | null\n      }) => ReturnType\n      /**\n       * Toggle a link mark\n       * @param attributes The link attributes\n       * @example editor.commands.toggleLink({ href: 'https://tiptap.dev' })\n       */\n      toggleLink: (attributes?: {\n        href: string\n        target?: string | null\n        rel?: string | null\n        class?: string | null\n      }) => ReturnType\n      /**\n       * Unset a link mark\n       * @example editor.commands.unsetLink()\n       */\n      unsetLink: () => ReturnType\n    }\n  }\n}\n\nexport function isAllowedUri(uri: string | undefined, protocols?: LinkOptions['protocols']) {\n  const allowedProtocols: string[] = ['http', 'https', 'ftp', 'ftps', 'mailto', 'tel', 'callto', 'sms', 'cid', 'xmpp']\n\n  if (protocols) {\n    protocols.forEach(protocol => {\n      const nextProtocol = typeof protocol === 'string' ? protocol : protocol.scheme\n\n      if (nextProtocol) {\n        allowedProtocols.push(nextProtocol)\n      }\n    })\n  }\n\n  return (\n    !uri ||\n    uri.replace(UNICODE_WHITESPACE_REGEX_GLOBAL, '').match(\n      new RegExp(\n        // eslint-disable-next-line no-useless-escape\n        `^(?:(?:${allowedProtocols.join('|')}):|[^a-z]|[a-z0-9+.\\-]+(?:[^a-z+.\\-:]|$))`,\n        'i',\n      ),\n    )\n  )\n}\n\n/**\n * This extension allows you to create links.\n * @see https://www.tiptap.dev/api/marks/link\n */\nexport const Link = Mark.create<LinkOptions>({\n  name: 'link',\n\n  priority: 1000,\n\n  keepOnSplit: false,\n\n  exitable: true,\n\n  onCreate() {\n    if (this.options.validate && !this.options.shouldAutoLink) {\n      // Copy the validate function to the shouldAutoLink option\n      this.options.shouldAutoLink = this.options.validate\n      console.warn('The `validate` option is deprecated. Rename to the `shouldAutoLink` option instead.')\n    }\n    this.options.protocols.forEach(protocol => {\n      if (typeof protocol === 'string') {\n        registerCustomProtocol(protocol)\n        return\n      }\n      registerCustomProtocol(protocol.scheme, protocol.optionalSlashes)\n    })\n  },\n\n  onDestroy() {\n    reset()\n  },\n\n  inclusive() {\n    return this.options.autolink\n  },\n\n  addOptions() {\n    return {\n      openOnClick: true,\n      enableClickSelection: false,\n      linkOnPaste: true,\n      autolink: true,\n      protocols: [],\n      defaultProtocol: 'http',\n      HTMLAttributes: {\n        target: '_blank',\n        rel: 'noopener noreferrer nofollow',\n        class: null,\n      },\n      isAllowedUri: (url, ctx) => !!isAllowedUri(url, ctx.protocols),\n      validate: url => !!url,\n      shouldAutoLink: url => !!url,\n    }\n  },\n\n  addAttributes() {\n    return {\n      href: {\n        default: null,\n        parseHTML(element) {\n          return element.getAttribute('href')\n        },\n      },\n      target: {\n        default: this.options.HTMLAttributes.target,\n      },\n      rel: {\n        default: this.options.HTMLAttributes.rel,\n      },\n      class: {\n        default: this.options.HTMLAttributes.class,\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'a[href]',\n        getAttrs: dom => {\n          const href = (dom as HTMLElement).getAttribute('href')\n\n          // prevent XSS attacks\n          if (\n            !href ||\n            !this.options.isAllowedUri(href, {\n              defaultValidate: url => !!isAllowedUri(url, this.options.protocols),\n              protocols: this.options.protocols,\n              defaultProtocol: this.options.defaultProtocol,\n            })\n          ) {\n            return false\n          }\n          return null\n        },\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    // prevent XSS attacks\n    if (\n      !this.options.isAllowedUri(HTMLAttributes.href, {\n        defaultValidate: href => !!isAllowedUri(href, this.options.protocols),\n        protocols: this.options.protocols,\n        defaultProtocol: this.options.defaultProtocol,\n      })\n    ) {\n      // strip out the href\n      return ['a', mergeAttributes(this.options.HTMLAttributes, { ...HTMLAttributes, href: '' }), 0]\n    }\n\n    return ['a', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setLink:\n        attributes =>\n        ({ chain }) => {\n          const { href } = attributes\n\n          if (\n            !this.options.isAllowedUri(href, {\n              defaultValidate: url => !!isAllowedUri(url, this.options.protocols),\n              protocols: this.options.protocols,\n              defaultProtocol: this.options.defaultProtocol,\n            })\n          ) {\n            return false\n          }\n\n          return chain().setMark(this.name, attributes).setMeta('preventAutolink', true).run()\n        },\n\n      toggleLink:\n        attributes =>\n        ({ chain }) => {\n          const { href } = attributes || {}\n\n          if (\n            href &&\n            !this.options.isAllowedUri(href, {\n              defaultValidate: url => !!isAllowedUri(url, this.options.protocols),\n              protocols: this.options.protocols,\n              defaultProtocol: this.options.defaultProtocol,\n            })\n          ) {\n            return false\n          }\n\n          return chain()\n            .toggleMark(this.name, attributes, { extendEmptyMarkRange: true })\n            .setMeta('preventAutolink', true)\n            .run()\n        },\n\n      unsetLink:\n        () =>\n        ({ chain }) => {\n          return chain().unsetMark(this.name, { extendEmptyMarkRange: true }).setMeta('preventAutolink', true).run()\n        },\n    }\n  },\n\n  addPasteRules() {\n    return [\n      markPasteRule({\n        find: text => {\n          const foundLinks: PasteRuleMatch[] = []\n\n          if (text) {\n            const { protocols, defaultProtocol } = this.options\n            const links = find(text).filter(\n              item =>\n                item.isLink &&\n                this.options.isAllowedUri(item.value, {\n                  defaultValidate: href => !!isAllowedUri(href, protocols),\n                  protocols,\n                  defaultProtocol,\n                }),\n            )\n\n            if (links.length) {\n              links.forEach(link =>\n                foundLinks.push({\n                  text: link.value,\n                  data: {\n                    href: link.href,\n                  },\n                  index: link.start,\n                }),\n              )\n            }\n          }\n\n          return foundLinks\n        },\n        type: this.type,\n        getAttributes: match => {\n          return {\n            href: match.data?.href,\n          }\n        },\n      }),\n    ]\n  },\n\n  addProseMirrorPlugins() {\n    const plugins: Plugin[] = []\n    const { protocols, defaultProtocol } = this.options\n\n    if (this.options.autolink) {\n      plugins.push(\n        autolink({\n          type: this.type,\n          defaultProtocol: this.options.defaultProtocol,\n          validate: url =>\n            this.options.isAllowedUri(url, {\n              defaultValidate: href => !!isAllowedUri(href, protocols),\n              protocols,\n              defaultProtocol,\n            }),\n          shouldAutoLink: this.options.shouldAutoLink,\n        }),\n      )\n    }\n\n    if (this.options.openOnClick === true) {\n      plugins.push(\n        clickHandler({\n          type: this.type,\n          editor: this.editor,\n          enableClickSelection: this.options.enableClickSelection,\n        }),\n      )\n    }\n\n    if (this.options.linkOnPaste) {\n      plugins.push(\n        pasteHandler({\n          editor: this.editor,\n          defaultProtocol: this.options.defaultProtocol,\n          type: this.type,\n        }),\n      )\n    }\n\n    return plugins\n  },\n})\n", "import type { NodeWithPos } from '@tiptap/core'\nimport { combineTransactionSteps, findChildrenInRange, getChangedRanges, getMarksBetween } from '@tiptap/core'\nimport type { MarkType } from '@tiptap/pm/model'\nimport { <PERSON>lug<PERSON>, Plugin<PERSON>ey } from '@tiptap/pm/state'\nimport type { MultiToken } from 'linkifyjs'\nimport { tokenize } from 'linkifyjs'\n\nimport { UNICODE_WHITESPACE_REGEX, UNICODE_WHITESPACE_REGEX_END } from './whitespace.js'\n\n/**\n * Check if the provided tokens form a valid link structure, which can either be a single link token\n * or a link token surrounded by parentheses or square brackets.\n *\n * This ensures that only complete and valid text is hyperlinked, preventing cases where a valid\n * top-level domain (TLD) is immediately followed by an invalid character, like a number. For\n * example, with the `find` method from Linkify, entering `example.com1` would result in\n * `example.com` being linked and the trailing `1` left as plain text. By using the `tokenize`\n * method, we can perform more comprehensive validation on the input text.\n */\nfunction isValidLinkStructure(tokens: Array<ReturnType<MultiToken['toObject']>>) {\n  if (tokens.length === 1) {\n    return tokens[0].isLink\n  }\n\n  if (tokens.length === 3 && tokens[1].isLink) {\n    return ['()', '[]'].includes(tokens[0].value + tokens[2].value)\n  }\n\n  return false\n}\n\ntype AutolinkOptions = {\n  type: MarkType\n  defaultProtocol: string\n  validate: (url: string) => boolean\n  shouldAutoLink: (url: string) => boolean\n}\n\n/**\n * This plugin allows you to automatically add links to your editor.\n * @param options The plugin options\n * @returns The plugin instance\n */\nexport function autolink(options: AutolinkOptions): Plugin {\n  return new Plugin({\n    key: new PluginKey('autolink'),\n    appendTransaction: (transactions, oldState, newState) => {\n      /**\n       * Does the transaction change the document?\n       */\n      const docChanges = transactions.some(transaction => transaction.docChanged) && !oldState.doc.eq(newState.doc)\n\n      /**\n       * Prevent autolink if the transaction is not a document change or if the transaction has the meta `preventAutolink`.\n       */\n      const preventAutolink = transactions.some(transaction => transaction.getMeta('preventAutolink'))\n\n      /**\n       * Prevent autolink if the transaction is not a document change\n       * or if the transaction has the meta `preventAutolink`.\n       */\n      if (!docChanges || preventAutolink) {\n        return\n      }\n\n      const { tr } = newState\n      const transform = combineTransactionSteps(oldState.doc, [...transactions])\n      const changes = getChangedRanges(transform)\n\n      changes.forEach(({ newRange }) => {\n        // Now let’s see if we can add new links.\n        const nodesInChangedRanges = findChildrenInRange(newState.doc, newRange, node => node.isTextblock)\n\n        let textBlock: NodeWithPos | undefined\n        let textBeforeWhitespace: string | undefined\n\n        if (nodesInChangedRanges.length > 1) {\n          // Grab the first node within the changed ranges (ex. the first of two paragraphs when hitting enter).\n          textBlock = nodesInChangedRanges[0]\n          textBeforeWhitespace = newState.doc.textBetween(\n            textBlock.pos,\n            textBlock.pos + textBlock.node.nodeSize,\n            undefined,\n            ' ',\n          )\n        } else if (nodesInChangedRanges.length) {\n          const endText = newState.doc.textBetween(newRange.from, newRange.to, ' ', ' ')\n          if (!UNICODE_WHITESPACE_REGEX_END.test(endText)) {\n            return\n          }\n          textBlock = nodesInChangedRanges[0]\n          textBeforeWhitespace = newState.doc.textBetween(textBlock.pos, newRange.to, undefined, ' ')\n        }\n\n        if (textBlock && textBeforeWhitespace) {\n          const wordsBeforeWhitespace = textBeforeWhitespace.split(UNICODE_WHITESPACE_REGEX).filter(Boolean)\n\n          if (wordsBeforeWhitespace.length <= 0) {\n            return false\n          }\n\n          const lastWordBeforeSpace = wordsBeforeWhitespace[wordsBeforeWhitespace.length - 1]\n          const lastWordAndBlockOffset = textBlock.pos + textBeforeWhitespace.lastIndexOf(lastWordBeforeSpace)\n\n          if (!lastWordBeforeSpace) {\n            return false\n          }\n\n          const linksBeforeSpace = tokenize(lastWordBeforeSpace).map(t => t.toObject(options.defaultProtocol))\n\n          if (!isValidLinkStructure(linksBeforeSpace)) {\n            return false\n          }\n\n          linksBeforeSpace\n            .filter(link => link.isLink)\n            // Calculate link position.\n            .map(link => ({\n              ...link,\n              from: lastWordAndBlockOffset + link.start + 1,\n              to: lastWordAndBlockOffset + link.end + 1,\n            }))\n            // ignore link inside code mark\n            .filter(link => {\n              if (!newState.schema.marks.code) {\n                return true\n              }\n\n              return !newState.doc.rangeHasMark(link.from, link.to, newState.schema.marks.code)\n            })\n            // validate link\n            .filter(link => options.validate(link.value))\n            // check whether should autolink\n            .filter(link => options.shouldAutoLink(link.value))\n            // Add link mark.\n            .forEach(link => {\n              if (getMarksBetween(link.from, link.to, newState.doc).some(item => item.mark.type === options.type)) {\n                return\n              }\n\n              tr.addMark(\n                link.from,\n                link.to,\n                options.type.create({\n                  href: link.href,\n                }),\n              )\n            })\n        }\n      })\n\n      if (!tr.steps.length) {\n        return\n      }\n\n      return tr\n    },\n  })\n}\n", "// From DOMPurify\n// https://github.com/cure53/DOMPurify/blob/main/src/regexp.ts\nexport const UNICODE_WHITESPACE_PATTERN = '[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]'\n\nexport const UNICODE_WHITESPACE_REGEX = new RegExp(UNICODE_WHITESPACE_PATTERN)\nexport const UNICODE_WHITESPACE_REGEX_END = new RegExp(`${UNICODE_WHITESPACE_PATTERN}$`)\nexport const UNICODE_WHITESPACE_REGEX_GLOBAL = new RegExp(UNICODE_WHITESPACE_PATTERN, 'g')\n", "import type { Editor } from '@tiptap/core'\nimport { getAttributes } from '@tiptap/core'\nimport type { MarkType } from '@tiptap/pm/model'\nimport { Plugin, PluginKey } from '@tiptap/pm/state'\n\ntype ClickHandlerOptions = {\n  type: MarkType\n  editor: Editor\n  enableClickSelection?: boolean\n}\n\nexport function clickHandler(options: ClickHandlerOptions): Plugin {\n  return new Plugin({\n    key: new PluginKey('handleClickLink'),\n    props: {\n      handleClick: (view, pos, event) => {\n        if (event.button !== 0) {\n          return false\n        }\n\n        if (!view.editable) {\n          return false\n        }\n\n        let link: HTMLAnchorElement | null = null\n\n        if (event.target instanceof HTMLAnchorElement) {\n          link = event.target\n        } else {\n          let a = event.target as HTMLElement\n          const els = []\n\n          while (a.nodeName !== 'DIV') {\n            els.push(a)\n            a = a.parentNode as HTMLElement\n          }\n          link = els.find(value => value.nodeName === 'A') as HTMLAnchorElement\n        }\n\n        if (!link) {\n          return false\n        }\n\n        const attrs = getAttributes(view.state, options.type.name)\n        const href = link?.href ?? attrs.href\n        const target = link?.target ?? attrs.target\n\n        if (options.enableClickSelection) {\n          options.editor.commands.extendMarkRange(options.type.name)\n        }\n\n        if (link && href) {\n          window.open(href, target)\n\n          return true\n        }\n\n        return false\n      },\n    },\n  })\n}\n", "import type { Editor } from '@tiptap/core'\nimport type { MarkType } from '@tiptap/pm/model'\nimport { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state'\nimport { find } from 'linkifyjs'\n\ntype PasteHandlerOptions = {\n  editor: Editor\n  defaultProtocol: string\n  type: MarkType\n}\n\nexport function pasteHandler(options: PasteHandlerOptions): Plugin {\n  return new Plugin({\n    key: new PluginKey('handlePasteLink'),\n    props: {\n      handlePaste: (view, event, slice) => {\n        const { state } = view\n        const { selection } = state\n        const { empty } = selection\n\n        if (empty) {\n          return false\n        }\n\n        let textContent = ''\n\n        slice.content.forEach(node => {\n          textContent += node.textContent\n        })\n\n        const link = find(textContent, { defaultProtocol: options.defaultProtocol }).find(\n          item => item.isLink && item.value === textContent,\n        )\n\n        if (!textContent || !link) {\n          return false\n        }\n\n        return options.editor.commands.setMark(options.type, {\n          href: link.href,\n        })\n      },\n    },\n  })\n}\n", "import { Link } from './link.js'\n\nexport * from './link.js'\n\nexport default Link\n"], "mappings": ";AACA,SAAS,MAAM,eAAe,uBAAuB;AAErD,SAAS,QAAAA,OAAM,wBAAwB,aAAa;;;ACFpD,SAAS,yBAAyB,qBAAqB,kBAAkB,uBAAuB;AAEhG,SAAS,QAAQ,iBAAiB;AAElC,SAAS,gBAAgB;;;ACHlB,IAAM,6BAA6B;AAEnC,IAAM,2BAA2B,IAAI,OAAO,0BAA0B;AACtE,IAAM,+BAA+B,IAAI,OAAO,GAAG,0BAA0B,GAAG;AAChF,IAAM,kCAAkC,IAAI,OAAO,4BAA4B,GAAG;;;ADazF,SAAS,qBAAqB,QAAmD;AAC/E,MAAI,OAAO,WAAW,GAAG;AACvB,WAAO,OAAO,CAAC,EAAE;AAAA,EACnB;AAEA,MAAI,OAAO,WAAW,KAAK,OAAO,CAAC,EAAE,QAAQ;AAC3C,WAAO,CAAC,MAAM,IAAI,EAAE,SAAS,OAAO,CAAC,EAAE,QAAQ,OAAO,CAAC,EAAE,KAAK;AAAA,EAChE;AAEA,SAAO;AACT;AAcO,SAAS,SAAS,SAAkC;AACzD,SAAO,IAAI,OAAO;AAAA,IAChB,KAAK,IAAI,UAAU,UAAU;AAAA,IAC7B,mBAAmB,CAAC,cAAc,UAAU,aAAa;AAIvD,YAAM,aAAa,aAAa,KAAK,iBAAe,YAAY,UAAU,KAAK,CAAC,SAAS,IAAI,GAAG,SAAS,GAAG;AAK5G,YAAM,kBAAkB,aAAa,KAAK,iBAAe,YAAY,QAAQ,iBAAiB,CAAC;AAM/F,UAAI,CAAC,cAAc,iBAAiB;AAClC;AAAA,MACF;AAEA,YAAM,EAAE,GAAG,IAAI;AACf,YAAM,YAAY,wBAAwB,SAAS,KAAK,CAAC,GAAG,YAAY,CAAC;AACzE,YAAM,UAAU,iBAAiB,SAAS;AAE1C,cAAQ,QAAQ,CAAC,EAAE,SAAS,MAAM;AAEhC,cAAM,uBAAuB,oBAAoB,SAAS,KAAK,UAAU,UAAQ,KAAK,WAAW;AAEjG,YAAI;AACJ,YAAI;AAEJ,YAAI,qBAAqB,SAAS,GAAG;AAEnC,sBAAY,qBAAqB,CAAC;AAClC,iCAAuB,SAAS,IAAI;AAAA,YAClC,UAAU;AAAA,YACV,UAAU,MAAM,UAAU,KAAK;AAAA,YAC/B;AAAA,YACA;AAAA,UACF;AAAA,QACF,WAAW,qBAAqB,QAAQ;AACtC,gBAAM,UAAU,SAAS,IAAI,YAAY,SAAS,MAAM,SAAS,IAAI,KAAK,GAAG;AAC7E,cAAI,CAAC,6BAA6B,KAAK,OAAO,GAAG;AAC/C;AAAA,UACF;AACA,sBAAY,qBAAqB,CAAC;AAClC,iCAAuB,SAAS,IAAI,YAAY,UAAU,KAAK,SAAS,IAAI,QAAW,GAAG;AAAA,QAC5F;AAEA,YAAI,aAAa,sBAAsB;AACrC,gBAAM,wBAAwB,qBAAqB,MAAM,wBAAwB,EAAE,OAAO,OAAO;AAEjG,cAAI,sBAAsB,UAAU,GAAG;AACrC,mBAAO;AAAA,UACT;AAEA,gBAAM,sBAAsB,sBAAsB,sBAAsB,SAAS,CAAC;AAClF,gBAAM,yBAAyB,UAAU,MAAM,qBAAqB,YAAY,mBAAmB;AAEnG,cAAI,CAAC,qBAAqB;AACxB,mBAAO;AAAA,UACT;AAEA,gBAAM,mBAAmB,SAAS,mBAAmB,EAAE,IAAI,OAAK,EAAE,SAAS,QAAQ,eAAe,CAAC;AAEnG,cAAI,CAAC,qBAAqB,gBAAgB,GAAG;AAC3C,mBAAO;AAAA,UACT;AAEA,2BACG,OAAO,UAAQ,KAAK,MAAM,EAE1B,IAAI,WAAS;AAAA,YACZ,GAAG;AAAA,YACH,MAAM,yBAAyB,KAAK,QAAQ;AAAA,YAC5C,IAAI,yBAAyB,KAAK,MAAM;AAAA,UAC1C,EAAE,EAED,OAAO,UAAQ;AACd,gBAAI,CAAC,SAAS,OAAO,MAAM,MAAM;AAC/B,qBAAO;AAAA,YACT;AAEA,mBAAO,CAAC,SAAS,IAAI,aAAa,KAAK,MAAM,KAAK,IAAI,SAAS,OAAO,MAAM,IAAI;AAAA,UAClF,CAAC,EAEA,OAAO,UAAQ,QAAQ,SAAS,KAAK,KAAK,CAAC,EAE3C,OAAO,UAAQ,QAAQ,eAAe,KAAK,KAAK,CAAC,EAEjD,QAAQ,UAAQ;AACf,gBAAI,gBAAgB,KAAK,MAAM,KAAK,IAAI,SAAS,GAAG,EAAE,KAAK,UAAQ,KAAK,KAAK,SAAS,QAAQ,IAAI,GAAG;AACnG;AAAA,YACF;AAEA,eAAG;AAAA,cACD,KAAK;AAAA,cACL,KAAK;AAAA,cACL,QAAQ,KAAK,OAAO;AAAA,gBAClB,MAAM,KAAK;AAAA,cACb,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AAAA,QACL;AAAA,MACF,CAAC;AAED,UAAI,CAAC,GAAG,MAAM,QAAQ;AACpB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACH;;;AE7JA,SAAS,qBAAqB;AAE9B,SAAS,UAAAC,SAAQ,aAAAC,kBAAiB;AAQ3B,SAAS,aAAa,SAAsC;AACjE,SAAO,IAAID,QAAO;AAAA,IAChB,KAAK,IAAIC,WAAU,iBAAiB;AAAA,IACpC,OAAO;AAAA,MACL,aAAa,CAAC,MAAM,KAAK,UAAU;AAfzC;AAgBQ,YAAI,MAAM,WAAW,GAAG;AACtB,iBAAO;AAAA,QACT;AAEA,YAAI,CAAC,KAAK,UAAU;AAClB,iBAAO;AAAA,QACT;AAEA,YAAI,OAAiC;AAErC,YAAI,MAAM,kBAAkB,mBAAmB;AAC7C,iBAAO,MAAM;AAAA,QACf,OAAO;AACL,cAAI,IAAI,MAAM;AACd,gBAAM,MAAM,CAAC;AAEb,iBAAO,EAAE,aAAa,OAAO;AAC3B,gBAAI,KAAK,CAAC;AACV,gBAAI,EAAE;AAAA,UACR;AACA,iBAAO,IAAI,KAAK,WAAS,MAAM,aAAa,GAAG;AAAA,QACjD;AAEA,YAAI,CAAC,MAAM;AACT,iBAAO;AAAA,QACT;AAEA,cAAM,QAAQ,cAAc,KAAK,OAAO,QAAQ,KAAK,IAAI;AACzD,cAAM,QAAO,kCAAM,SAAN,YAAc,MAAM;AACjC,cAAM,UAAS,kCAAM,WAAN,YAAgB,MAAM;AAErC,YAAI,QAAQ,sBAAsB;AAChC,kBAAQ,OAAO,SAAS,gBAAgB,QAAQ,KAAK,IAAI;AAAA,QAC3D;AAEA,YAAI,QAAQ,MAAM;AAChB,iBAAO,KAAK,MAAM,MAAM;AAExB,iBAAO;AAAA,QACT;AAEA,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF,CAAC;AACH;;;AC3DA,SAAS,UAAAC,SAAQ,aAAAC,kBAAiB;AAClC,SAAS,YAAY;AAQd,SAAS,aAAa,SAAsC;AACjE,SAAO,IAAID,QAAO;AAAA,IAChB,KAAK,IAAIC,WAAU,iBAAiB;AAAA,IACpC,OAAO;AAAA,MACL,aAAa,CAAC,MAAM,OAAO,UAAU;AACnC,cAAM,EAAE,MAAM,IAAI;AAClB,cAAM,EAAE,UAAU,IAAI;AACtB,cAAM,EAAE,MAAM,IAAI;AAElB,YAAI,OAAO;AACT,iBAAO;AAAA,QACT;AAEA,YAAI,cAAc;AAElB,cAAM,QAAQ,QAAQ,UAAQ;AAC5B,yBAAe,KAAK;AAAA,QACtB,CAAC;AAED,cAAM,OAAO,KAAK,aAAa,EAAE,iBAAiB,QAAQ,gBAAgB,CAAC,EAAE;AAAA,UAC3E,UAAQ,KAAK,UAAU,KAAK,UAAU;AAAA,QACxC;AAEA,YAAI,CAAC,eAAe,CAAC,MAAM;AACzB,iBAAO;AAAA,QACT;AAEA,eAAO,QAAQ,OAAO,SAAS,QAAQ,QAAQ,MAAM;AAAA,UACnD,MAAM,KAAK;AAAA,QACb,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,CAAC;AACH;;;AJjBO,IAAM,aACX;AAwIK,SAAS,aAAa,KAAyB,WAAsC;AAC1F,QAAM,mBAA6B,CAAC,QAAQ,SAAS,OAAO,QAAQ,UAAU,OAAO,UAAU,OAAO,OAAO,MAAM;AAEnH,MAAI,WAAW;AACb,cAAU,QAAQ,cAAY;AAC5B,YAAM,eAAe,OAAO,aAAa,WAAW,WAAW,SAAS;AAExE,UAAI,cAAc;AAChB,yBAAiB,KAAK,YAAY;AAAA,MACpC;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SACE,CAAC,OACD,IAAI,QAAQ,iCAAiC,EAAE,EAAE;AAAA,IAC/C,IAAI;AAAA;AAAA,MAEF,UAAU,iBAAiB,KAAK,GAAG,CAAC;AAAA,MACpC;AAAA,IACF;AAAA,EACF;AAEJ;AAMO,IAAM,OAAO,KAAK,OAAoB;AAAA,EAC3C,MAAM;AAAA,EAEN,UAAU;AAAA,EAEV,aAAa;AAAA,EAEb,UAAU;AAAA,EAEV,WAAW;AACT,QAAI,KAAK,QAAQ,YAAY,CAAC,KAAK,QAAQ,gBAAgB;AAEzD,WAAK,QAAQ,iBAAiB,KAAK,QAAQ;AAC3C,cAAQ,KAAK,qFAAqF;AAAA,IACpG;AACA,SAAK,QAAQ,UAAU,QAAQ,cAAY;AACzC,UAAI,OAAO,aAAa,UAAU;AAChC,+BAAuB,QAAQ;AAC/B;AAAA,MACF;AACA,6BAAuB,SAAS,QAAQ,SAAS,eAAe;AAAA,IAClE,CAAC;AAAA,EACH;AAAA,EAEA,YAAY;AACV,UAAM;AAAA,EACR;AAAA,EAEA,YAAY;AACV,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EAEA,aAAa;AACX,WAAO;AAAA,MACL,aAAa;AAAA,MACb,sBAAsB;AAAA,MACtB,aAAa;AAAA,MACb,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,MACZ,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,QACd,QAAQ;AAAA,QACR,KAAK;AAAA,QACL,OAAO;AAAA,MACT;AAAA,MACA,cAAc,CAAC,KAAK,QAAQ,CAAC,CAAC,aAAa,KAAK,IAAI,SAAS;AAAA,MAC7D,UAAU,SAAO,CAAC,CAAC;AAAA,MACnB,gBAAgB,SAAO,CAAC,CAAC;AAAA,IAC3B;AAAA,EACF;AAAA,EAEA,gBAAgB;AACd,WAAO;AAAA,MACL,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,UAAU,SAAS;AACjB,iBAAO,QAAQ,aAAa,MAAM;AAAA,QACpC;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,SAAS,KAAK,QAAQ,eAAe;AAAA,MACvC;AAAA,MACA,KAAK;AAAA,QACH,SAAS,KAAK,QAAQ,eAAe;AAAA,MACvC;AAAA,MACA,OAAO;AAAA,QACL,SAAS,KAAK,QAAQ,eAAe;AAAA,MACvC;AAAA,IACF;AAAA,EACF;AAAA,EAEA,YAAY;AACV,WAAO;AAAA,MACL;AAAA,QACE,KAAK;AAAA,QACL,UAAU,SAAO;AACf,gBAAM,OAAQ,IAAoB,aAAa,MAAM;AAGrD,cACE,CAAC,QACD,CAAC,KAAK,QAAQ,aAAa,MAAM;AAAA,YAC/B,iBAAiB,SAAO,CAAC,CAAC,aAAa,KAAK,KAAK,QAAQ,SAAS;AAAA,YAClE,WAAW,KAAK,QAAQ;AAAA,YACxB,iBAAiB,KAAK,QAAQ;AAAA,UAChC,CAAC,GACD;AACA,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,WAAW,EAAE,eAAe,GAAG;AAE7B,QACE,CAAC,KAAK,QAAQ,aAAa,eAAe,MAAM;AAAA,MAC9C,iBAAiB,UAAQ,CAAC,CAAC,aAAa,MAAM,KAAK,QAAQ,SAAS;AAAA,MACpE,WAAW,KAAK,QAAQ;AAAA,MACxB,iBAAiB,KAAK,QAAQ;AAAA,IAChC,CAAC,GACD;AAEA,aAAO,CAAC,KAAK,gBAAgB,KAAK,QAAQ,gBAAgB,EAAE,GAAG,gBAAgB,MAAM,GAAG,CAAC,GAAG,CAAC;AAAA,IAC/F;AAEA,WAAO,CAAC,KAAK,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;AAAA,EAC9E;AAAA,EAEA,cAAc;AACZ,WAAO;AAAA,MACL,SACE,gBACA,CAAC,EAAE,MAAM,MAAM;AACb,cAAM,EAAE,KAAK,IAAI;AAEjB,YACE,CAAC,KAAK,QAAQ,aAAa,MAAM;AAAA,UAC/B,iBAAiB,SAAO,CAAC,CAAC,aAAa,KAAK,KAAK,QAAQ,SAAS;AAAA,UAClE,WAAW,KAAK,QAAQ;AAAA,UACxB,iBAAiB,KAAK,QAAQ;AAAA,QAChC,CAAC,GACD;AACA,iBAAO;AAAA,QACT;AAEA,eAAO,MAAM,EAAE,QAAQ,KAAK,MAAM,UAAU,EAAE,QAAQ,mBAAmB,IAAI,EAAE,IAAI;AAAA,MACrF;AAAA,MAEF,YACE,gBACA,CAAC,EAAE,MAAM,MAAM;AACb,cAAM,EAAE,KAAK,IAAI,cAAc,CAAC;AAEhC,YACE,QACA,CAAC,KAAK,QAAQ,aAAa,MAAM;AAAA,UAC/B,iBAAiB,SAAO,CAAC,CAAC,aAAa,KAAK,KAAK,QAAQ,SAAS;AAAA,UAClE,WAAW,KAAK,QAAQ;AAAA,UACxB,iBAAiB,KAAK,QAAQ;AAAA,QAChC,CAAC,GACD;AACA,iBAAO;AAAA,QACT;AAEA,eAAO,MAAM,EACV,WAAW,KAAK,MAAM,YAAY,EAAE,sBAAsB,KAAK,CAAC,EAChE,QAAQ,mBAAmB,IAAI,EAC/B,IAAI;AAAA,MACT;AAAA,MAEF,WACE,MACA,CAAC,EAAE,MAAM,MAAM;AACb,eAAO,MAAM,EAAE,UAAU,KAAK,MAAM,EAAE,sBAAsB,KAAK,CAAC,EAAE,QAAQ,mBAAmB,IAAI,EAAE,IAAI;AAAA,MAC3G;AAAA,IACJ;AAAA,EACF;AAAA,EAEA,gBAAgB;AACd,WAAO;AAAA,MACL,cAAc;AAAA,QACZ,MAAM,UAAQ;AACZ,gBAAM,aAA+B,CAAC;AAEtC,cAAI,MAAM;AACR,kBAAM,EAAE,WAAW,gBAAgB,IAAI,KAAK;AAC5C,kBAAM,QAAQC,MAAK,IAAI,EAAE;AAAA,cACvB,UACE,KAAK,UACL,KAAK,QAAQ,aAAa,KAAK,OAAO;AAAA,gBACpC,iBAAiB,UAAQ,CAAC,CAAC,aAAa,MAAM,SAAS;AAAA,gBACvD;AAAA,gBACA;AAAA,cACF,CAAC;AAAA,YACL;AAEA,gBAAI,MAAM,QAAQ;AAChB,oBAAM;AAAA,gBAAQ,UACZ,WAAW,KAAK;AAAA,kBACd,MAAM,KAAK;AAAA,kBACX,MAAM;AAAA,oBACJ,MAAM,KAAK;AAAA,kBACb;AAAA,kBACA,OAAO,KAAK;AAAA,gBACd,CAAC;AAAA,cACH;AAAA,YACF;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAAA,QACA,MAAM,KAAK;AAAA,QACX,eAAe,WAAS;AApYhC;AAqYU,iBAAO;AAAA,YACL,OAAM,WAAM,SAAN,mBAAY;AAAA,UACpB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEA,wBAAwB;AACtB,UAAM,UAAoB,CAAC;AAC3B,UAAM,EAAE,WAAW,gBAAgB,IAAI,KAAK;AAE5C,QAAI,KAAK,QAAQ,UAAU;AACzB,cAAQ;AAAA,QACN,SAAS;AAAA,UACP,MAAM,KAAK;AAAA,UACX,iBAAiB,KAAK,QAAQ;AAAA,UAC9B,UAAU,SACR,KAAK,QAAQ,aAAa,KAAK;AAAA,YAC7B,iBAAiB,UAAQ,CAAC,CAAC,aAAa,MAAM,SAAS;AAAA,YACvD;AAAA,YACA;AAAA,UACF,CAAC;AAAA,UACH,gBAAgB,KAAK,QAAQ;AAAA,QAC/B,CAAC;AAAA,MACH;AAAA,IACF;AAEA,QAAI,KAAK,QAAQ,gBAAgB,MAAM;AACrC,cAAQ;AAAA,QACN,aAAa;AAAA,UACX,MAAM,KAAK;AAAA,UACX,QAAQ,KAAK;AAAA,UACb,sBAAsB,KAAK,QAAQ;AAAA,QACrC,CAAC;AAAA,MACH;AAAA,IACF;AAEA,QAAI,KAAK,QAAQ,aAAa;AAC5B,cAAQ;AAAA,QACN,aAAa;AAAA,UACX,QAAQ,KAAK;AAAA,UACb,iBAAiB,KAAK,QAAQ;AAAA,UAC9B,MAAM,KAAK;AAAA,QACb,CAAC;AAAA,MACH;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AACF,CAAC;;;AKnbD,IAAO,gBAAQ;", "names": ["find", "Plugin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Plugin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "find"]}