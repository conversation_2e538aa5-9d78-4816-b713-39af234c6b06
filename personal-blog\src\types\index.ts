export interface User {
  id: string
  email: string
  full_name: string | null
  avatar_url: string | null
  role: 'admin' | 'user'
  created_at: string
  updated_at: string
}

export interface Category {
  id: string
  name: string
  slug: string
  description: string | null
  created_at: string
  updated_at: string
  _count?: {
    posts: number
  }
}

export interface Post {
  id: string
  title: string
  slug: string
  content: string
  excerpt: string | null
  featured_image: string | null
  status: 'draft' | 'published'
  author_id: string
  category_id: string | null
  created_at: string
  updated_at: string
  published_at: string | null
  meta_title: string | null
  meta_description: string | null
  author?: User
  category?: Category
  comments?: Comment[]
  _count?: {
    comments: number
  }
}

export interface Comment {
  id: string
  content: string
  post_id: string
  author_id: string
  parent_id: string | null
  status: 'pending' | 'approved' | 'rejected'
  created_at: string
  updated_at: string
  author?: User
  replies?: Comment[]
  post?: Post
}

export interface SiteSetting {
  id: string
  key: string
  value: string
  created_at: string
  updated_at: string
}

export interface CreatePostData {
  title: string
  content: string
  excerpt?: string
  featured_image?: string
  status: 'draft' | 'published'
  category_id?: string
  meta_title?: string
  meta_description?: string
}

export interface UpdatePostData extends Partial<CreatePostData> {
  id: string
}

export interface CreateCommentData {
  content: string
  post_id: string
  parent_id?: string
}

export interface CreateCategoryData {
  name: string
  description?: string
}

export interface UpdateCategoryData extends Partial<CreateCategoryData> {
  id: string
}

export interface AuthUser {
  id: string
  email: string
  role: 'admin' | 'user'
  full_name?: string
  avatar_url?: string
}

export interface PaginationParams {
  page?: number
  limit?: number
  search?: string
  category?: string
  status?: 'draft' | 'published'
  sort?: 'newest' | 'oldest' | 'title'
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export interface SEOData {
  title: string
  description: string
  keywords?: string[]
  image?: string
  url?: string
  type?: 'website' | 'article'
  publishedTime?: string
  modifiedTime?: string
  author?: string
}

export interface NavigationItem {
  label: string
  href: string
  icon?: string
  children?: NavigationItem[]
}

export interface ThemeConfig {
  primaryColor: string
  secondaryColor: string
  accentColor: string
  backgroundColor: string
  textColor: string
  borderColor: string
}
