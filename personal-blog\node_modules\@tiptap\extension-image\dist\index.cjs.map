{"version": 3, "sources": ["../src/index.ts", "../src/image.ts"], "sourcesContent": ["import { Image } from './image.js'\n\nexport * from './image.js'\n\nexport default Image\n", "import { mergeAttributes, Node, nodeInputRule } from '@tiptap/core'\n\nexport interface ImageOptions {\n  /**\n   * Controls if the image node should be inline or not.\n   * @default false\n   * @example true\n   */\n  inline: boolean\n\n  /**\n   * Controls if base64 images are allowed. Enable this if you want to allow\n   * base64 image urls in the `src` attribute.\n   * @default false\n   * @example true\n   */\n  allowBase64: boolean\n\n  /**\n   * HTML attributes to add to the image element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n}\n\nexport interface SetImageOptions {\n  src: string\n  alt?: string\n  title?: string\n  width?: number\n  height?: number\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    image: {\n      /**\n       * Add an image\n       * @param options The image attributes\n       * @example\n       * editor\n       *   .commands\n       *   .setImage({ src: 'https://tiptap.dev/logo.png', alt: 'tiptap', title: 'tiptap logo' })\n       */\n      setImage: (options: SetImageOptions) => ReturnType\n    }\n  }\n}\n\n/**\n * Matches an image to a ![image](src \"title\") on input.\n */\nexport const inputRegex = /(?:^|\\s)(!\\[(.+|:?)]\\((\\S+)(?:(?:\\s+)[\"'](\\S+)[\"'])?\\))$/\n\n/**\n * This extension allows you to insert images.\n * @see https://www.tiptap.dev/api/nodes/image\n */\nexport const Image = Node.create<ImageOptions>({\n  name: 'image',\n\n  addOptions() {\n    return {\n      inline: false,\n      allowBase64: false,\n      HTMLAttributes: {},\n    }\n  },\n\n  inline() {\n    return this.options.inline\n  },\n\n  group() {\n    return this.options.inline ? 'inline' : 'block'\n  },\n\n  draggable: true,\n\n  addAttributes() {\n    return {\n      src: {\n        default: null,\n      },\n      alt: {\n        default: null,\n      },\n      title: {\n        default: null,\n      },\n      width: {\n        default: null,\n      },\n      height: {\n        default: null,\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: this.options.allowBase64 ? 'img[src]' : 'img[src]:not([src^=\"data:\"])',\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['img', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)]\n  },\n\n  addCommands() {\n    return {\n      setImage:\n        options =>\n        ({ commands }) => {\n          return commands.insertContent({\n            type: this.name,\n            attrs: options,\n          })\n        },\n    }\n  },\n\n  addInputRules() {\n    return [\n      nodeInputRule({\n        find: inputRegex,\n        type: this.type,\n        getAttributes: match => {\n          const [, , alt, src, title] = match\n\n          return { src, alt, title }\n        },\n      }),\n    ]\n  },\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,kBAAqD;AAqD9C,IAAM,aAAa;AAMnB,IAAM,QAAQ,iBAAK,OAAqB;AAAA,EAC7C,MAAM;AAAA,EAEN,aAAa;AACX,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,gBAAgB,CAAC;AAAA,IACnB;AAAA,EACF;AAAA,EAEA,SAAS;AACP,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EAEA,QAAQ;AACN,WAAO,KAAK,QAAQ,SAAS,WAAW;AAAA,EAC1C;AAAA,EAEA,WAAW;AAAA,EAEX,gBAAgB;AACd,WAAO;AAAA,MACL,KAAK;AAAA,QACH,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,SAAS;AAAA,MACX;AAAA,MACA,OAAO;AAAA,QACL,SAAS;AAAA,MACX;AAAA,MACA,OAAO;AAAA,QACL,SAAS;AAAA,MACX;AAAA,MACA,QAAQ;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF;AAAA,EACF;AAAA,EAEA,YAAY;AACV,WAAO;AAAA,MACL;AAAA,QACE,KAAK,KAAK,QAAQ,cAAc,aAAa;AAAA,MAC/C;AAAA,IACF;AAAA,EACF;AAAA,EAEA,WAAW,EAAE,eAAe,GAAG;AAC7B,WAAO,CAAC,WAAO,6BAAgB,KAAK,QAAQ,gBAAgB,cAAc,CAAC;AAAA,EAC7E;AAAA,EAEA,cAAc;AACZ,WAAO;AAAA,MACL,UACE,aACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,cAAc;AAAA,UAC5B,MAAM,KAAK;AAAA,UACX,OAAO;AAAA,QACT,CAAC;AAAA,MACH;AAAA,IACJ;AAAA,EACF;AAAA,EAEA,gBAAgB;AACd,WAAO;AAAA,UACL,2BAAc;AAAA,QACZ,MAAM;AAAA,QACN,MAAM,KAAK;AAAA,QACX,eAAe,WAAS;AACtB,gBAAM,CAAC,EAAE,EAAE,KAAK,KAAK,KAAK,IAAI;AAE9B,iBAAO,EAAE,KAAK,KAAK,MAAM;AAAA,QAC3B;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;;;ADtID,IAAO,gBAAQ;", "names": []}