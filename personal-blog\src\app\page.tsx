import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON>Right, BookOpen, Users, MessageCircle, User, Sparkles, Zap } from "lucide-react";

export default function Home() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative container py-32 md:py-40 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 -z-10">
          <div className="absolute top-1/4 left-1/4 w-72 h-72 bg-primary/10 rounded-full blur-3xl animate-float" />
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }} />
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-primary/5 to-accent/5 rounded-full blur-3xl" />
        </div>

        <div className="mx-auto max-w-5xl text-center relative">
          <div className="animate-fade-in">
            <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold tracking-tight mb-8 leading-tight">
              Welcome to My
              <br />
              <span className="bg-gradient-to-r from-primary via-accent to-primary bg-clip-text text-transparent animate-gradient-shift bg-[length:200%_200%]">
                Personal Blog
              </span>
            </h1>
          </div>

          <div className="animate-slide-up" style={{ animationDelay: '0.2s' }}>
            <p className="text-xl md:text-2xl text-muted-foreground mb-12 max-w-3xl mx-auto leading-relaxed">
              A modern, beautiful space where I share my thoughts, experiences, and insights
              about technology, life, and everything in between.
            </p>
          </div>

          <div className="animate-scale-in flex flex-col sm:flex-row gap-6 justify-center" style={{ animationDelay: '0.4s' }}>
            <Button size="xl" variant="default" asChild className="group">
              <Link href="/blog">
                Read Latest Posts
                <ArrowRight className="ml-3 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </Link>
            </Button>
            <Button variant="outline" size="xl" asChild className="group">
              <Link href="/about">
                Learn About Me
                <User className="ml-3 h-5 w-5 transition-transform group-hover:scale-110" />
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="container py-24 relative">
        <div className="mx-auto max-w-6xl">
          <div className="text-center mb-20 animate-fade-in">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              What You'll Find Here
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Discover a world of insights, stories, and connections in our carefully crafted digital space
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12">
            <Card className="text-center group animate-slide-up hover:scale-105" style={{ animationDelay: '0.1s' }}>
              <CardHeader>
                <div className="mx-auto h-16 w-16 rounded-2xl bg-gradient-to-br from-primary/20 to-accent/20 flex items-center justify-center mb-6 group-hover:shadow-lg group-hover:shadow-primary/25 transition-all duration-300">
                  <BookOpen className="h-8 w-8 text-primary group-hover:scale-110 transition-transform duration-300" />
                </div>
                <CardTitle className="text-2xl mb-4">Quality Content</CardTitle>
                <CardDescription className="text-base leading-relaxed">
                  In-depth articles and tutorials crafted with care, covering technology, insights, and creative explorations
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="text-center group animate-slide-up hover:scale-105" style={{ animationDelay: '0.2s' }}>
              <CardHeader>
                <div className="mx-auto h-16 w-16 rounded-2xl bg-gradient-to-br from-accent/20 to-success/20 flex items-center justify-center mb-6 group-hover:shadow-lg group-hover:shadow-accent/25 transition-all duration-300">
                  <Users className="h-8 w-8 text-accent group-hover:scale-110 transition-transform duration-300" />
                </div>
                <CardTitle className="text-2xl mb-4">Community</CardTitle>
                <CardDescription className="text-base leading-relaxed">
                  Join vibrant discussions and connect with like-minded readers from around the world
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="text-center group animate-slide-up hover:scale-105" style={{ animationDelay: '0.3s' }}>
              <CardHeader>
                <div className="mx-auto h-16 w-16 rounded-2xl bg-gradient-to-br from-success/20 to-primary/20 flex items-center justify-center mb-6 group-hover:shadow-lg group-hover:shadow-success/25 transition-all duration-300">
                  <MessageCircle className="h-8 w-8 text-success group-hover:scale-110 transition-transform duration-300" />
                </div>
                <CardTitle className="text-2xl mb-4">Engagement</CardTitle>
                <CardDescription className="text-base leading-relaxed">
                  Share your thoughts and engage in meaningful conversations that inspire and educate
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container py-24 relative">
        <div className="mx-auto max-w-4xl text-center relative">
          {/* Background decoration */}
          <div className="absolute inset-0 -z-10">
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-primary/10 to-accent/10 rounded-full blur-3xl" />
          </div>

          <div className="bg-gradient-to-br from-card/80 to-card/40 backdrop-blur-sm rounded-3xl p-12 md:p-16 border border-border/50 shadow-2xl animate-fade-in">
            <div className="flex justify-center mb-8">
              <div className="h-16 w-16 rounded-2xl bg-gradient-to-br from-primary to-accent flex items-center justify-center shadow-lg animate-pulse-glow">
                <Sparkles className="h-8 w-8 text-white" />
              </div>
            </div>

            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              Ready to Start Reading?
            </h2>
            <p className="text-xl text-muted-foreground mb-10 max-w-2xl mx-auto leading-relaxed">
              Explore our latest articles and join our growing community of passionate readers and thinkers.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="xl" variant="default" asChild className="group">
                <Link href="/blog">
                  Browse All Posts
                  <ArrowRight className="ml-3 h-5 w-5 transition-transform group-hover:translate-x-1" />
                </Link>
              </Button>
              <Button size="xl" variant="outline" asChild className="group">
                <Link href="/auth/sign-up">
                  Join Community
                  <Zap className="ml-3 h-5 w-5 transition-transform group-hover:scale-110" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
