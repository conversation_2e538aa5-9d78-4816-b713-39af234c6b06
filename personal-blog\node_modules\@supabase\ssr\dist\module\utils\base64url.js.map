{"version": 3, "file": "base64url.js", "sourceRoot": "", "sources": ["../../../src/utils/base64url.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH;;;GAGG;AACH,MAAM,YAAY,GAChB,kEAAkE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AAE/E;;;GAGG;AACH,MAAM,gBAAgB,GAAG,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AAE9C;;;GAGG;AACH,MAAM,cAAc,GAAG,CAAC,GAAG,EAAE;IAC3B,MAAM,OAAO,GAAa,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;IAEzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;QAC3C,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAClB,CAAC;IAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;QACpD,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAClD,CAAC;IAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;QAChD,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC7C,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC,CAAC,EAAE,CAAC;AAEL;;;;;;GAMG;AACH,MAAM,UAAU,iBAAiB,CAAC,GAAW;IAC3C,MAAM,MAAM,GAAa,EAAE,CAAC;IAE5B,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,UAAU,GAAG,CAAC,CAAC;IAEnB,MAAM,OAAO,GAAG,CAAC,IAAY,EAAE,EAAE;QAC/B,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;QAC5B,UAAU,IAAI,CAAC,CAAC;QAEhB,OAAO,UAAU,IAAI,CAAC,EAAE,CAAC;YACvB,MAAM,GAAG,GAAG,CAAC,KAAK,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YAC7C,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;YAC/B,UAAU,IAAI,CAAC,CAAC;QAClB,CAAC;IACH,CAAC,CAAC;IAEF,YAAY,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IAE3B,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;QACnB,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC;QAClC,UAAU,GAAG,CAAC,CAAC;QAEf,OAAO,UAAU,IAAI,CAAC,EAAE,CAAC;YACvB,MAAM,GAAG,GAAG,CAAC,KAAK,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YAC7C,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;YAC/B,UAAU,IAAI,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACzB,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,mBAAmB,CAAC,GAAW;IAC7C,MAAM,IAAI,GAAa,EAAE,CAAC;IAE1B,MAAM,IAAI,GAAG,CAAC,SAAiB,EAAE,EAAE;QACjC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC;IAEF,MAAM,KAAK,GAAG;QACZ,OAAO,EAAE,CAAC;QACV,SAAS,EAAE,CAAC;KACb,CAAC;IAEF,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,UAAU,GAAG,CAAC,CAAC;IAEnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;QACvC,MAAM,SAAS,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACpC,MAAM,IAAI,GAAG,cAAc,CAAC,SAAS,CAAC,CAAC;QAEvC,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC;YACd,6BAA6B;YAC7B,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;YAC5B,UAAU,IAAI,CAAC,CAAC;YAEhB,OAAO,UAAU,IAAI,CAAC,EAAE,CAAC;gBACvB,cAAc,CAAC,CAAC,KAAK,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;gBAChE,UAAU,IAAI,CAAC,CAAC;YAClB,CAAC;QACH,CAAC;aAAM,IAAI,IAAI,KAAK,CAAC,CAAC,EAAE,CAAC;YACvB,mCAAmC;YACnC,SAAS;QACX,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CACb,iCAAiC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,iBAAiB,CAAC,EAAE,CAC/D,CAAC;QACJ,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACvB,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,eAAe,CAC7B,SAAiB,EACjB,IAA4B;IAE5B,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,SAAS,CAAC,CAAC;QAChB,OAAO;IACT,CAAC;SAAM,IAAI,SAAS,IAAI,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,IAAI,GAAG,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,IAAI,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC;QAChC,OAAO;IACT,CAAC;SAAM,IAAI,SAAS,IAAI,MAAM,EAAE,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,IAAI,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC;QAChC,OAAO;IACT,CAAC;SAAM,IAAI,SAAS,IAAI,QAAQ,EAAE,CAAC;QACjC,IAAI,CAAC,IAAI,GAAG,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,SAAS,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QACxC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,IAAI,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC;QAChC,OAAO;IACT,CAAC;IAED,MAAM,IAAI,KAAK,CAAC,mCAAmC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC/E,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,YAAY,CAAC,GAAW,EAAE,IAA4B;IACpE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;QACvC,IAAI,SAAS,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAElC,IAAI,SAAS,GAAG,MAAM,IAAI,SAAS,IAAI,MAAM,EAAE,CAAC;YAC9C,uEAAuE;YACvE,sEAAsE;YACtE,2CAA2C;YAC3C,MAAM,aAAa,GAAG,CAAC,CAAC,SAAS,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC;YAC9D,MAAM,YAAY,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC;YAC/D,SAAS,GAAG,CAAC,YAAY,GAAG,aAAa,CAAC,GAAG,OAAO,CAAC;YACrD,CAAC,IAAI,CAAC,CAAC;QACT,CAAC;QAED,eAAe,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IACnC,CAAC;AACH,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,cAAc,CAC5B,IAAY,EACZ,KAA6C,EAC7C,IAAiC;IAEjC,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC,EAAE,CAAC;QACxB,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;YACjB,IAAI,CAAC,IAAI,CAAC,CAAC;YACX,OAAO;QACT,CAAC;QAED,uDAAuD;QACvD,KAAK,IAAI,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,CAAC,EAAE,UAAU,IAAI,CAAC,EAAE,CAAC;YACzD,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC3C,KAAK,CAAC,OAAO,GAAG,UAAU,CAAC;gBAC3B,MAAM;YACR,CAAC;QACH,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC,EAAE,CAAC;YACxB,KAAK,CAAC,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;QAC9B,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC,EAAE,CAAC;YAC/B,KAAK,CAAC,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;QAC9B,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC,EAAE,CAAC;YAC/B,KAAK,CAAC,SAAS,GAAG,IAAI,GAAG,CAAC,CAAC;QAC7B,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC5C,CAAC;QAED,KAAK,CAAC,OAAO,IAAI,CAAC,CAAC;IACrB,CAAC;SAAM,IAAI,KAAK,CAAC,OAAO,GAAG,CAAC,EAAE,CAAC;QAC7B,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC5C,CAAC;QAED,KAAK,CAAC,SAAS,GAAG,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC;QACvD,KAAK,CAAC,OAAO,IAAI,CAAC,CAAC;QAEnB,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC,EAAE,CAAC;YACxB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QACxB,CAAC;IACH,CAAC;AACH,CAAC"}